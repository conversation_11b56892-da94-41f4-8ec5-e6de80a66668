@echo off
title Clarify - Interface de Traitement Automatique

echo.
echo CLARIFY - INTERFACE DE TRAITEMENT AUTOMATIQUE
echo ============================================================
echo.

REM Afficher le repertoire courant pour debug
echo Repertoire courant: %CD%
echo.

REM Verifier si le fichier lancer_interface.py existe
if not exist "lancer_interface.py" (
    echo ERREUR: Le fichier lancer_interface.py n'existe pas dans ce repertoire
    echo Contenu du repertoire:
    dir /b *.py
    echo.
    echo Assurez-vous d'etre dans le bon repertoire
    echo.
    pause
    exit /b 1
)

echo OK: Fichier lancer_interface.py trouve
echo.

REM Verifier si Python est installe
echo Verification de Python...
python --version 2>nul
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    echo.
    echo Installez Python depuis https://python.org
    echo Assurez-vous de cocher "Add Python to PATH" lors de l'installation
    echo.
    echo Tentative avec py.exe...
    py --version 2>nul
    if errorlevel 1 (
        echo ERREUR: py.exe non plus n'est pas disponible
        pause
        exit /b 1
    ) else (
        echo OK: py.exe detecte, utilisation de py.exe
        set PYTHON_CMD=py
    )
) else (
    echo OK: Python detecte
    set PYTHON_CMD=python
)

echo.

REM Lancer l'interface
echo Lancement de l'interface avec %PYTHON_CMD%...
echo.
%PYTHON_CMD% lancer_interface.py

REM Si on arrive ici, c'est que le script s'est termine
echo.
echo L'interface s'est fermee
echo Code de sortie: %ERRORLEVEL%
pause
