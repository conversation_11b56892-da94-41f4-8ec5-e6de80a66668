# 📦 Installation de l'Interface Clarify

Guide d'installation complet pour l'interface graphique de traitement automatique Clarify.

## 🎯 Vue d'ensemble

Cette interface permet de contrôler le script de traitement automatique Clarify avec :
- Interface graphique intuitive
- Boutons Start/Stop
- Console intégrée en temps réel
- Sélection de fichier Excel
- Suivi automatique des traitements

## 📋 Prérequis

### 1. Python 3.7+
- **Windows** : Téléchargez depuis [python.org](https://python.org)
- **Important** : Cochez "Add Python to PATH" lors de l'installation

### 2. Clarify (Amdocs CRM)
- Application Clarify installée et fonctionnelle
- Accès à votre environnement Clarify

## 🛠️ Installation

### Étape 1 : Télécharger les fichiers

Assurez-vous d'avoir tous ces fichiers dans le même dossier :

```
📁 Clarify_Interface/
├── 📄 lancer_interface.py              # Lanceur principal
├── 📄 clarify_gui.py                   # Interface graphique
├── 📄 clic_ID_2_simple.py              # Script de traitement original
├── 📄 config_interface.py              # Configuration
├── 📄 test_interface.py                # Tests
├── 📄 Lancer_Clarify_Interface.bat     # Lanceur Windows
├── 📄 TT_liste.xlsx                    # Fichier Excel exemple
└── 📁 image/
    ├── 🖼️ search_icon_selected.png      # Template recherche
    └── 🖼️ suivi_resolution.png          # Template suivi résolution
```

### Étape 2 : Installer les dépendances Python

Ouvrez un terminal/invite de commandes dans le dossier et tapez :

```bash
pip install pandas openpyxl pywin32 pyautogui opencv-python pillow
```

**Alternative avec requirements.txt** (si fourni) :
```bash
pip install -r requirements.txt
```

### Étape 3 : Tester l'installation

Lancez le test pour vérifier que tout fonctionne :

```bash
python test_interface.py
```

Vous devriez voir :
```
🎉 TOUS LES TESTS SONT PASSÉS!
✅ L'interface est prête à être utilisée
```

## 🚀 Premier lancement

### Méthode 1 : Double-clic (Windows)
1. Double-cliquez sur `Lancer_Clarify_Interface.bat`

### Méthode 2 : Ligne de commande
1. Ouvrez un terminal dans le dossier
2. Tapez : `python lancer_interface.py`

### Méthode 3 : Direct
1. Tapez : `python clarify_gui.py`

## 📊 Préparation des données

### Format du fichier Excel

Créez un fichier `TT_liste.xlsx` avec cette structure :

| A        |
|----------|
| ID_001   |
| ID_002   |
| ID_003   |

- **Colonne A** : IDs des cas à traiter
- **Pas d'en-tête** nécessaire
- **Format** : .xlsx (Excel)

### Templates d'images

Les fichiers dans le dossier `image/` sont des captures d'écran de :
- `search_icon_selected.png` : Icône de recherche dans Clarify
- `suivi_resolution.png` : Bouton "Suivi Résolution"

**Important** : Ces templates doivent correspondre à votre version de Clarify.

## 🔧 Configuration

### Configuration de base

Modifiez `config_interface.py` pour personnaliser :

```python
INTERFACE_CONFIG = {
    'window_width': 900,           # Largeur fenêtre
    'window_height': 700,          # Hauteur fenêtre
    'default_excel_file': "TT_liste.xlsx",  # Fichier par défaut
    'delay_between_cases': 2.0,    # Délai entre cas (secondes)
    # ... autres paramètres
}
```

### Configuration avancée

Créez un fichier `config_custom.py` pour vos paramètres personnalisés :

```python
# Mes paramètres personnalisés
INTERFACE_CONFIG = {
    'window_width': 1200,
    'delay_between_cases': 1.5,
    'console_height': 30
}
```

## 🎮 Utilisation

### Démarrage
1. **Ouvrez Clarify** et connectez-vous
2. **Lancez l'interface** avec une des méthodes ci-dessus
3. **Sélectionnez votre fichier Excel** avec le bouton "Parcourir"
4. **Cliquez sur START** pour commencer

### Pendant le traitement
- **Console** : Affiche le progrès en temps réel
- **STOP** : Arrête le traitement à tout moment
- **Suivi** : Automatiquement sauvé dans `Clarify_Suivi_Traitement.xlsx`

### Après le traitement
- Consultez le fichier de suivi pour les résultats détaillés
- Les cas échoués peuvent être retraités

## 🆘 Dépannage

### Problèmes courants

#### "Python n'est pas reconnu"
```bash
# Solution
# Réinstallez Python en cochant "Add Python to PATH"
# OU ajoutez Python au PATH manuellement
```

#### "Module non trouvé"
```bash
# Solution
pip install [nom_du_module]
# Exemple :
pip install pandas
```

#### "Clarify non trouvé"
- Vérifiez que Clarify est ouvert
- Le titre doit contenir "Amdocs CRM" ou "CLARIFY"
- Essayez de redémarrer Clarify

#### "Template non trouvé"
- Vérifiez que le dossier `image/` existe
- Recapturez les templates si nécessaire
- Ajustez la résolution d'écran si différente

#### "Erreur Excel"
- Vérifiez le format (.xlsx)
- Fermez Excel si le fichier est ouvert
- Vérifiez que les IDs sont en colonne A

### Logs et debug

- **Console intégrée** : Tous les messages en temps réel
- **Fichier de suivi** : Détails de chaque cas traité
- **Test** : `python test_interface.py` pour diagnostiquer

### Réinstallation complète

Si tout échoue :

1. **Désinstallez Python** (optionnel)
2. **Réinstallez Python** avec "Add to PATH"
3. **Supprimez le dossier** de l'interface
4. **Retéléchargez** tous les fichiers
5. **Réinstallez** les dépendances
6. **Relancez** les tests

## 📞 Support

### Auto-diagnostic
1. Lancez `python test_interface.py`
2. Vérifiez les messages d'erreur
3. Consultez la console de l'interface

### Informations utiles
- Version de Python : `python --version`
- Version des modules : `pip list`
- Système d'exploitation
- Version de Clarify

### Fichiers de log
- Console de l'interface (copiable)
- `Clarify_Suivi_Traitement.xlsx`
- Messages d'erreur Windows

---

**Version** : 1.0  
**Dernière mise à jour** : 2025-01-03  
**Compatibilité** : Windows 10/11, Python 3.7+
