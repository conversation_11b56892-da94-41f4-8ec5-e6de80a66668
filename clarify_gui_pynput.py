"""
Interface graphique pour l'automatisation Clarify avec pynput
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import sys
import time
import pandas as pd
import win32gui
import win32con
import win32ui
from ctypes import windll
from PIL import Image
import cv2
import numpy as np
import random
from datetime import datetime
import json

# Import pynput
try:
    from pynput.keyboard import Key, Controller as KeyboardController
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False

class ClarifyGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Clarify Automation - pynput")
        self.root.geometry("800x700")
        
        # Variables
        self.excel_file = tk.StringVar(value="TT_liste.xlsx")
        self.cases_data = []  # Maintenant on stocke {id, code_d}
        self.is_running = False
        self.keyboard = None
        self.tracking_file = None
        self.session_id = None
        
        # Initialiser pynput si disponible
        if PYNPUT_AVAILABLE:
            self.keyboard = KeyboardController()
        
        self.create_widgets()
        self.check_initial_files()
    
    def create_widgets(self):
        """Crée l'interface graphique"""
        
        # Titre
        title_frame = ttk.Frame(self.root)
        title_frame.pack(pady=10)
        
        title_label = ttk.Label(title_frame, text="🚀 Clarify Automation", font=("Arial", 16, "bold"))
        title_label.pack()
        
        # Section fichier Excel
        file_frame = ttk.LabelFrame(self.root, text="📁 Fichier Excel", padding=10)
        file_frame.pack(fill="x", padx=10, pady=5)
        
        file_entry_frame = ttk.Frame(file_frame)
        file_entry_frame.pack(fill="x")
        
        ttk.Entry(file_entry_frame, textvariable=self.excel_file, width=50).pack(side="left", fill="x", expand=True)
        ttk.Button(file_entry_frame, text="Parcourir", command=self.browse_file).pack(side="right", padx=(5,0))
        
        ttk.Button(file_frame, text="🔄 Charger les IDs", command=self.load_ids).pack(pady=(5,0))
        
        # Section IDs
        ids_frame = ttk.LabelFrame(self.root, text="📋 IDs à traiter", padding=10)
        ids_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Liste des IDs avec scrollbar
        list_frame = ttk.Frame(ids_frame)
        list_frame.pack(fill="both", expand=True)
        
        self.ids_listbox = tk.Listbox(list_frame, height=8)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.ids_listbox.yview)
        self.ids_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.ids_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Informations
        self.info_label = ttk.Label(ids_frame, text="Aucun ID chargé", foreground="gray")
        self.info_label.pack(pady=(5,0))
        
        # Section contrôles
        control_frame = ttk.LabelFrame(self.root, text="🎮 Contrôles", padding=10)
        control_frame.pack(fill="x", padx=10, pady=5)
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack()
        
        self.start_button = ttk.Button(button_frame, text="🚀 Démarrer", command=self.start_automation, style="Accent.TButton")
        self.start_button.pack(side="left", padx=5)

        self.start_tt_only_button = ttk.Button(button_frame, text="📋 Ouvrir/Accepter TT uniquement", command=self.start_tt_only_automation)
        self.start_tt_only_button.pack(side="left", padx=5)

        self.stop_button = ttk.Button(button_frame, text="🛑 Arrêter", command=self.stop_automation, state="disabled")
        self.stop_button.pack(side="left", padx=5)

        ttk.Button(button_frame, text="🔍 Tester Clarify", command=self.test_clarify).pack(side="left", padx=5)
        
        # Barre de progression
        self.progress = ttk.Progressbar(control_frame, mode='determinate')
        self.progress.pack(fill="x", pady=(10,0))
        
        # Zone de log
        log_frame = ttk.LabelFrame(self.root, text="📝 Journal", padding=10)
        log_frame.pack(fill="x", padx=10, pady=5)
        
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill="x")
        
        self.log_text = tk.Text(log_text_frame, height=6, wrap="word")
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")
    
    def log(self, message):
        """Ajoute un message au journal"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def browse_file(self):
        """Ouvre le dialogue de sélection de fichier"""
        filename = filedialog.askopenfilename(
            title="Sélectionner le fichier Excel",
            filetypes=[("Fichiers Excel", "*.xlsx *.xls"), ("Tous les fichiers", "*.*")]
        )
        if filename:
            self.excel_file.set(filename)
            self.load_ids()
    
    def load_ids(self):
        """Charge les IDs et codes D depuis le fichier Excel"""
        try:
            file_path = self.excel_file.get()
            if not os.path.exists(file_path):
                self.log(f"❌ Fichier non trouvé: {file_path}")
                return

            self.log(f"📖 Lecture de {file_path}...")

            # Lire le fichier Excel - d'abord vérifier le nombre de colonnes
            try:
                # Lire d'abord une ligne pour vérifier le nombre de colonnes
                df_test = pd.read_excel(file_path, nrows=1, header=None)
                max_cols = len(df_test.columns)
                self.log(f"📊 Nombre de colonnes détectées: {max_cols}")

                # Déterminer quelles colonnes lire (maximum 5: A, B, C, D, E)
                cols_to_read = min(5, max_cols)
                usecols = list(range(cols_to_read))

                self.log(f"📋 Lecture des colonnes: {usecols}")
                df = pd.read_excel(file_path, usecols=usecols, header=None)

            except Exception as e:
                self.log(f"⚠️  Erreur lecture colonnes spécifiques: {e}")
                # Fallback: lire tout le fichier
                self.log("🔄 Tentative de lecture complète du fichier...")
                df = pd.read_excel(file_path, header=None)

            # Extraire les données avec gestion sécurisée des colonnes
            cases_data = []
            for _, row in df.iterrows():
                # Colonne A (ID) - obligatoire
                case_id = str(row[0]).strip() if len(row) > 0 and pd.notna(row[0]) else ""

                # Colonnes B, C, D, E - optionnelles
                code_b = str(row[1]).strip() if len(row) > 1 and pd.notna(row[1]) else ""
                code_c = str(row[2]).strip() if len(row) > 2 and pd.notna(row[2]) else ""
                code_d = str(row[3]).strip() if len(row) > 3 and pd.notna(row[3]) else ""
                code_e = str(row[4]).strip() if len(row) > 4 and pd.notna(row[4]) else ""

                # Filtrer les lignes vides
                if case_id and case_id.lower() != 'nan':
                    cases_data.append({
                        'id': case_id,
                        'code_b': code_b,
                        'code_c': code_c,
                        'code_d': code_d,
                        'code_e': code_e
                    })

            # Mettre à jour la liste
            self.cases_data = cases_data
            self.ids_listbox.delete(0, tk.END)
            for case_data in cases_data:
                display_text = case_data['id']
                info_parts = []
                if case_data['code_b']:
                    info_parts.append(f"B: {case_data['code_b']}")
                if case_data['code_c']:
                    info_parts.append(f"C: {case_data['code_c']}")
                if case_data['code_d']:
                    info_parts.append(f"D: {case_data['code_d']}")
                if case_data['code_e']:
                    info_parts.append(f"E: {case_data['code_e']}")

                if info_parts:
                    display_text += f" ({', '.join(info_parts)})"
                self.ids_listbox.insert(tk.END, display_text)

            self.info_label.config(text=f"✅ {len(cases_data)} cas chargé(s)", foreground="green")
            self.log(f"✅ {len(cases_data)} cas chargé(s)")

        except Exception as e:
            self.log(f"❌ Erreur lecture Excel: {e}")
            messagebox.showerror("Erreur", f"Impossible de lire le fichier Excel:\n{e}")
    
    def check_initial_files(self):
        """Vérifie les fichiers au démarrage"""
        if os.path.exists("TT_liste.xlsx"):
            self.load_ids()

        if not PYNPUT_AVAILABLE:
            self.log("❌ pynput non disponible - Installez avec: pip install pynput")
            messagebox.showwarning("Dépendance manquante", "pynput n'est pas installé.\nInstallez avec: pip install pynput")

    def create_tracking_file(self):
        """Crée un fichier de suivi pour la session"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.session_id = f"session_{timestamp}"
        self.tracking_file = f"suivi_traitement_{timestamp}.json"

        tracking_data = {
            "session_id": self.session_id,
            "start_time": datetime.now().isoformat(),
            "excel_file": self.excel_file.get(),
            "total_cases": len(self.cases_data),
            "cases": {}
        }

        # Initialiser tous les cas comme "NON_TRAITE"
        for case_data in self.cases_data:
            tracking_data["cases"][case_data['id']] = {
                "status": "NON_TRAITE",
                "start_time": None,
                "end_time": None,
                "error_message": None,
                "mode": None  # "COMPLET" ou "TT_UNIQUEMENT"
            }

        with open(self.tracking_file, 'w', encoding='utf-8') as f:
            json.dump(tracking_data, f, indent=2, ensure_ascii=False)

        self.log(f"📊 Fichier de suivi créé: {self.tracking_file}")
        return self.tracking_file

    def update_case_status(self, case_id, status, error_message=None, mode=None):
        """Met à jour le statut d'un cas dans le fichier de suivi"""
        if not self.tracking_file or not os.path.exists(self.tracking_file):
            return

        try:
            with open(self.tracking_file, 'r', encoding='utf-8') as f:
                tracking_data = json.load(f)

            if case_id not in tracking_data["cases"]:
                tracking_data["cases"][case_id] = {}

            case_info = tracking_data["cases"][case_id]
            case_info["status"] = status

            if status == "EN_COURS":
                case_info["start_time"] = datetime.now().isoformat()
                if mode:
                    case_info["mode"] = mode
            elif status in ["SUCCES", "ECHEC"]:
                case_info["end_time"] = datetime.now().isoformat()
                if error_message:
                    case_info["error_message"] = error_message

            with open(self.tracking_file, 'w', encoding='utf-8') as f:
                json.dump(tracking_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.log(f"⚠️  Erreur mise à jour suivi: {e}")

    def get_unprocessed_cases(self):
        """Retourne la liste des cas non traités"""
        if not self.tracking_file or not os.path.exists(self.tracking_file):
            return self.cases_data

        try:
            with open(self.tracking_file, 'r', encoding='utf-8') as f:
                tracking_data = json.load(f)

            unprocessed = []
            for case_data in self.cases_data:
                case_id = case_data['id']
                if case_id in tracking_data["cases"]:
                    status = tracking_data["cases"][case_id]["status"]
                    if status not in ["SUCCES"]:  # Reprendre les cas non réussis
                        unprocessed.append(case_data)
                else:
                    unprocessed.append(case_data)

            return unprocessed

        except Exception as e:
            self.log(f"⚠️  Erreur lecture suivi: {e}")
            return self.cases_data

    def show_tracking_summary(self):
        """Affiche un résumé du suivi"""
        if not self.tracking_file or not os.path.exists(self.tracking_file):
            return

        try:
            with open(self.tracking_file, 'r', encoding='utf-8') as f:
                tracking_data = json.load(f)

            total = len(tracking_data["cases"])
            succes = sum(1 for case in tracking_data["cases"].values() if case["status"] == "SUCCES")
            echec = sum(1 for case in tracking_data["cases"].values() if case["status"] == "ECHEC")
            en_cours = sum(1 for case in tracking_data["cases"].values() if case["status"] == "EN_COURS")
            non_traite = sum(1 for case in tracking_data["cases"].values() if case["status"] == "NON_TRAITE")

            self.log(f"📊 RÉSUMÉ DU SUIVI:")
            self.log(f"   📁 Fichier: {self.tracking_file}")
            self.log(f"   📋 Total: {total} cas")
            self.log(f"   ✅ Réussis: {succes}")
            self.log(f"   ❌ Échoués: {echec}")
            self.log(f"   🔄 En cours: {en_cours}")
            self.log(f"   ⏸️  Non traités: {non_traite}")

        except Exception as e:
            self.log(f"⚠️  Erreur résumé suivi: {e}")

    def check_and_confirm_resume(self):
        """Vérifie s'il y a des cas non traités et demande confirmation pour reprendre"""
        unprocessed_cases = self.get_unprocessed_cases()
        total_cases = len(self.cases_data)

        if len(unprocessed_cases) == total_cases:
            # Nouveau traitement complet
            self.log(f"📋 Nouveau traitement: {total_cases} cas à traiter")
            return unprocessed_cases

        elif len(unprocessed_cases) < total_cases:
            # Il y a des cas déjà traités - demander confirmation
            processed_count = total_cases - len(unprocessed_cases)

            self.log(f"⚠️  REPRISE DE TRAITEMENT DÉTECTÉE:")
            self.log(f"   📊 Total: {total_cases} cas")
            self.log(f"   ✅ Déjà traités: {processed_count} cas")
            self.log(f"   📋 Restants: {len(unprocessed_cases)} cas")

            # Afficher les cas restants
            if len(unprocessed_cases) <= 10:
                self.log(f"   📝 Cas restants: {', '.join([case['id'] for case in unprocessed_cases])}")
            else:
                first_cases = [case['id'] for case in unprocessed_cases[:5]]
                self.log(f"   📝 Premiers cas restants: {', '.join(first_cases)}... (+{len(unprocessed_cases)-5} autres)")

            # Demander confirmation avec dialogue personnalisé
            response = messagebox.askyesnocancel(
                "Reprise de traitement détectée",
                f"Un traitement précédent a été détecté:\n\n"
                f"• Total: {total_cases} cas\n"
                f"• Déjà traités: {processed_count} cas\n"
                f"• Restants: {len(unprocessed_cases)} cas\n\n"
                f"⚠️  ATTENTION:\n"
                f"• Assurez-vous qu'aucun dossier n'est ouvert dans Clarify\n"
                f"• Vérifiez que Clarify est dans un état propre\n\n"
                f"Voulez-vous reprendre le traitement?\n\n"
                f"• OUI: Reprendre avec les cas restants\n"
                f"• NON: Recommencer depuis le début\n"
                f"• ANNULER: Arrêter"
            )

            if response is True:
                # Reprendre avec les cas restants
                self.log(f"✅ Reprise confirmée: {len(unprocessed_cases)} cas à traiter")
                return unprocessed_cases
            elif response is False:
                # Recommencer depuis le début
                self.log(f"🔄 Redémarrage complet: {total_cases} cas à traiter")
                # Marquer tous les cas comme non traités
                self.reset_all_cases_status()
                return self.cases_data
            else:
                # Annuler
                self.log("❌ Traitement annulé par l'utilisateur")
                return None

        else:
            # Cas d'erreur
            self.log(f"⚠️  Erreur: Plus de cas non traités que de cas totaux")
            return self.cases_data

    def reset_all_cases_status(self):
        """Remet tous les cas au statut NON_TRAITE"""
        if not self.tracking_file or not os.path.exists(self.tracking_file):
            return

        try:
            with open(self.tracking_file, 'r', encoding='utf-8') as f:
                tracking_data = json.load(f)

            # Remettre tous les cas à NON_TRAITE
            for case_id in tracking_data["cases"]:
                tracking_data["cases"][case_id] = {
                    "status": "NON_TRAITE",
                    "start_time": None,
                    "end_time": None,
                    "error_message": None,
                    "mode": None
                }

            with open(self.tracking_file, 'w', encoding='utf-8') as f:
                json.dump(tracking_data, f, indent=2, ensure_ascii=False)

            self.log("🔄 Tous les cas remis à NON_TRAITE")

        except Exception as e:
            self.log(f"⚠️  Erreur reset statuts: {e}")
    
    def find_clarify_window(self):
        """Trouve la fenêtre Clarify"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if "Amdocs CRM - ClearSupport" in window_text or "CLARIFY" in window_text:
                    windows.append((hwnd, window_text))
            return True
         
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        return windows
    
    def activate_clarify_window(self, hwnd):
        """Active la fenêtre Clarify et s'assure qu'elle a le focus"""
        try:
            self.log("🔄 Activation de Clarify...")

            # Restaurer et activer
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            win32gui.SetForegroundWindow(hwnd)
            win32gui.BringWindowToTop(hwnd)
            time.sleep(1)

            # Vérifier le focus plusieurs fois
            for attempt in range(3):
                foreground_hwnd = win32gui.GetForegroundWindow()
                if foreground_hwnd == hwnd:
                    self.log("✅ Clarify activé et au premier plan!")
                    return True

                # Réessayer l'activation
                self.log(f"🔄 Tentative {attempt + 1}/3 d'activation...")
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)

            self.log("⚠️  Clarify activé mais focus incertain")
            return True

        except Exception as e:
            self.log(f"❌ Erreur activation Clarify: {e}")
            return False

    def capture_window(self, hwnd):
        """Capture la fenêtre Clarify"""
        try:
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()

            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]

            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)

            result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 3)

            if result:
                bmpinfo = saveBitMap.GetInfo()
                bmpstr = saveBitMap.GetBitmapBits(True)
                img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                                     bmpstr, 'raw', 'BGRX', 0, 1)

                # Nettoyage
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)

                return img

            return None

        except Exception as e:
            self.log(f"❌ Erreur capture: {e}")
            return None

    def detect_and_click_image(self, hwnd, template_path, description="image"):
        """Détecte et clique sur une image template dans Clarify"""

        if not os.path.exists(template_path):
            self.log(f"❌ Template {description} non trouvé: {template_path}")
            return False

        try:
            self.log(f"🎯 Recherche de {description}...")

            # Capture de la fenêtre
            screenshot = self.capture_window(hwnd)
            if screenshot is None:
                self.log(f"❌ Impossible de capturer la fenêtre")
                return False

            # Conversion pour détection
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            template = cv2.imread(template_path)

            if template is not None:
                # Détection
                screenshot_gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

                result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                self.log(f"📊 Score détection {description}: {max_val:.3f}")

                # Seuils pour différents types d'images
                thresholds = [0.7, 0.6, 0.5, 0.4]

                for threshold in thresholds:
                    if max_val >= threshold:
                        template_h, template_w = template_gray.shape
                        center_x = max_loc[0] + template_w // 2
                        center_y = max_loc[1] + template_h // 2

                        self.log(f"✅ {description} détecté! (seuil: {threshold})")
                        self.log(f"   Position: ({center_x}, {center_y})")

                        # Clic avec pynput
                        rect = win32gui.GetWindowRect(hwnd)
                        abs_x = rect[0] + center_x
                        abs_y = rect[1] + center_y

                        self.log(f"👆 Clic sur {description} à ({abs_x}, {abs_y})...")

                        try:
                            from pynput import mouse

                            mouse_controller = mouse.Controller()
                            mouse_controller.position = (abs_x, abs_y)
                            time.sleep(0.1)
                            mouse_controller.click(mouse.Button.left, 1)
                            time.sleep(0.5)

                            self.log(f"✅ Clic effectué sur {description}")
                            return True

                        except Exception as e:
                            self.log(f"❌ Erreur clic: {e}")
                            return False

                self.log(f"❌ {description} non détecté (score max: {max_val:.3f})")
                return False
            else:
                self.log(f"❌ Impossible de charger le template {description}")
                return False

        except Exception as e:
            self.log(f"❌ Erreur détection {description}: {e}")
            return False
    
    def test_clarify(self):
        """Teste la connexion avec Clarify"""
        windows = self.find_clarify_window()
        if not windows:
            self.log("❌ Clarify non trouvé!")
            messagebox.showerror("Erreur", "Clarify non trouvé!\nAssurez-vous que Clarify est ouvert.")
            return
        
        hwnd, title = windows[0]
        self.log(f"✅ Clarify trouvé: {title}")
        
        if self.activate_clarify_window(hwnd):
            messagebox.showinfo("Test réussi", f"Clarify trouvé et activé:\n{title}")
        else:
            messagebox.showerror("Test échoué", "Impossible d'activer Clarify")
    
    def start_automation(self):
        """Démarre l'automatisation complète"""
        if not PYNPUT_AVAILABLE:
            messagebox.showerror("Erreur", "pynput n'est pas disponible!")
            return

        if not self.cases_data:
            messagebox.showerror("Erreur", "Aucun cas à traiter!\nChargez d'abord un fichier Excel.")
            return

        # Vérifier Clarify
        windows = self.find_clarify_window()
        if not windows:
            messagebox.showerror("Erreur", "Clarify non trouvé!\nAssurez-vous que Clarify est ouvert.")
            return

        # Créer le fichier de suivi
        self.create_tracking_file()

        # Vérifier s'il y a des cas non traités et demander confirmation
        cases_to_process = self.check_and_confirm_resume()
        if not cases_to_process:
            return  # Utilisateur a annulé

        # Démarrage automatique sans confirmation
        self.log(f"🚀 Démarrage automatique du traitement de {len(cases_to_process)} cas...")
        self.log("💡 Assurez-vous que:")
        self.log("   - Clarify est ouvert")
        self.log("   - Vous êtes dans le bon module")
        self.log("   - Aucune boîte de dialogue n'est ouverte")
        self.log("   - Aucun dossier n'est ouvert dans Clarify")

        # Démarrer en arrière-plan
        self.is_running = True
        self.start_button.config(state="disabled")
        self.start_tt_only_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.progress.config(maximum=len(cases_to_process), value=0)

        # Lancer dans un thread séparé
        thread = threading.Thread(target=self.run_automation, args=(windows[0], cases_to_process))
        thread.daemon = True
        thread.start()

    def start_tt_only_automation(self):
        """Démarre l'automatisation TT uniquement (Ouvrir/Accepter seulement)"""
        if not PYNPUT_AVAILABLE:
            messagebox.showerror("Erreur", "pynput n'est pas disponible!")
            return

        if not self.cases_data:
            messagebox.showerror("Erreur", "Aucun cas à traiter!\nChargez d'abord un fichier Excel.")
            return

        # Vérifier Clarify
        windows = self.find_clarify_window()
        if not windows:
            messagebox.showerror("Erreur", "Clarify non trouvé!\nAssurez-vous que Clarify est ouvert.")
            return

        # Créer le fichier de suivi
        self.create_tracking_file()

        # Vérifier s'il y a des cas non traités et demander confirmation
        cases_to_process = self.check_and_confirm_resume()
        if not cases_to_process:
            return  # Utilisateur a annulé

        # Démarrage automatique sans confirmation
        self.log(f"📋 Démarrage TT uniquement (Ouvrir/Accepter) - {len(cases_to_process)} cas...")
        self.log("💡 Mode: Ouvrir → Accepter → Fermer (sans saisie de données)")
        self.log("💡 Assurez-vous que:")
        self.log("   - Clarify est ouvert")
        self.log("   - Vous êtes dans le bon module")
        self.log("   - Aucune boîte de dialogue n'est ouverte")
        self.log("   - Aucun dossier n'est ouvert dans Clarify")

        # Démarrer en arrière-plan
        self.is_running = True
        self.start_button.config(state="disabled")
        self.start_tt_only_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.progress.config(maximum=len(cases_to_process), value=0)

        # Lancer dans un thread séparé
        thread = threading.Thread(target=self.run_tt_only_automation, args=(windows[0], cases_to_process))
        thread.daemon = True
        thread.start()
    
    def stop_automation(self):
        """Arrête l'automatisation"""
        self.is_running = False
        self.start_button.config(state="normal")
        self.start_tt_only_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.log("🛑 Arrêt demandé...")
    
    def run_automation(self, clarify_window, cases_to_process):
        """Exécute l'automatisation"""
        hwnd, title = clarify_window
        
        try:
            # Activer Clarify
            if not self.activate_clarify_window(hwnd):
                self.log("❌ Impossible d'activer Clarify")
                return
            
            success_count = 0
            failed_count = 0
            
            for i, case_data in enumerate(cases_to_process):
                if not self.is_running:
                    self.log("🛑 Traitement interrompu")
                    break

                case_id = case_data['id']
                code_b = case_data['code_b']
                code_c = case_data['code_c']
                code_d = case_data['code_d']
                code_e = case_data['code_e']

                self.log(f"🔄 Traitement {i+1}/{len(cases_to_process)}: {case_id}")
                if code_b:
                    self.log(f"📋 Code B: {code_b}")
                if code_c:
                    self.log(f"📋 Code C: {code_c}")
                if code_d:
                    self.log(f"📋 Code D: {code_d}")
                if code_e:
                    self.log(f"📋 Code E: {code_e}")

                # Marquer le cas comme en cours
                self.update_case_status(case_id, "EN_COURS", mode="COMPLET")

                # S'assurer que Clarify a toujours le focus avant chaque cas
                try:
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(0.3)
                except:
                    pass

                if self.process_single_case(case_data):
                    success_count += 1
                    self.log(f"✅ Cas {case_id} réussi")
                    self.update_case_status(case_id, "SUCCES")
                else:
                    failed_count += 1
                    self.log(f"❌ Cas {case_id} échoué")
                    self.update_case_status(case_id, "ECHEC", "Erreur pendant le traitement")

                # Mettre à jour la progression
                self.progress.config(value=i+1)

                # Pause entre les cas
                if i < len(cases_to_process) - 1 and self.is_running:
                    self.log("⏸️  Pause 3 secondes...")
                    time.sleep(3)
            
            # Résumé final
            self.log(f"🎉 Terminé! Réussis: {success_count}, Échoués: {failed_count}")
            self.show_tracking_summary()
            
        except Exception as e:
            self.log(f"❌ Erreur fatale: {e}")
        
        finally:
            self.is_running = False
            self.start_button.config(state="normal")
            self.start_tt_only_button.config(state="normal")
            self.stop_button.config(state="disabled")

    def run_tt_only_automation(self, clarify_window, cases_to_process):
        """Exécute l'automatisation TT uniquement (Ouvrir/Accepter seulement)"""
        hwnd, title = clarify_window

        try:
            # Activer Clarify
            if not self.activate_clarify_window(hwnd):
                self.log("❌ Impossible d'activer Clarify")
                return

            success_count = 0
            failed_count = 0

            # Traiter chaque cas
            for i, case_data in enumerate(cases_to_process, 1):
                if not self.is_running:
                    self.log("🛑 Arrêt demandé")
                    break

                case_id = case_data['id']
                self.log(f"\n{'='*30}")
                self.log(f"CAS TT {i}/{len(cases_to_process)}: {case_id}")
                self.log(f"{'='*30}")

                # Marquer le cas comme en cours
                self.update_case_status(case_id, "EN_COURS", mode="TT_UNIQUEMENT")

                if self.process_tt_only_case(case_data):
                    success_count += 1
                    self.log(f"✅ Cas TT {case_id} réussi")
                    self.update_case_status(case_id, "SUCCES")
                else:
                    failed_count += 1
                    self.log(f"❌ Cas TT {case_id} échoué")
                    self.update_case_status(case_id, "ECHEC", "Erreur pendant le traitement TT uniquement")

                # Mettre à jour la progression
                self.progress.config(value=i+1)

                # Pause entre les cas
                if i < len(cases_to_process) - 1 and self.is_running:
                    self.log("⏸️  Pause 3 secondes...")
                    time.sleep(3)

            # Résumé final
            self.log(f"🎉 TT uniquement terminé! Réussis: {success_count}, Échoués: {failed_count}")
            self.show_tracking_summary()

        except Exception as e:
            self.log(f"❌ Erreur fatale TT uniquement: {e}")

        finally:
            self.is_running = False
            self.start_button.config(state="normal")
            self.start_tt_only_button.config(state="normal")
            self.stop_button.config(state="disabled")
    
    def send_key(self, key_char):
        """Envoie une touche simple avec pynput"""
        try:
            self.keyboard.press(key_char)
            self.keyboard.release(key_char)
            time.sleep(0.3)
            return True
        except Exception as e:
            self.log(f"   ❌ Erreur envoi touche {key_char}: {e}")
            return False

    def open_dropdown_and_select(self, target_option, navigation_keys):
        """Ouvre un menu déroulant avec V et navigue avec les touches spécifiées"""
        try:
            self.log(f"   🔽 Ouverture menu déroulant pour '{target_option}'...")

            # Appuyer sur V pour ouvrir le menu déroulant
            if not self.send_key('v'):
                self.log("      ❌ Échec ouverture menu (touche V)")
                return False

            time.sleep(0.2)  # Pause pour laisser le menu s'ouvrir (0.4s / 2)

            # Naviguer avec les touches spécifiées
            self.log(f"      🧭 Navigation: {navigation_keys}")
            for key in navigation_keys:
                if not self.send_key(key):
                    self.log(f"      ❌ Échec navigation (touche {key})")
                    return False
                time.sleep(0.125)  # Pause entre chaque navigation (0.25s / 2)

            # Appuyer sur Entrée pour valider la sélection
            self.log("      ⏎ Validation avec Entrée...")
            self.keyboard.press(Key.enter)
            self.keyboard.release(Key.enter)
            time.sleep(0.5)

            self.log(f"      ✅ '{target_option}' sélectionné avec succès!")
            return True

        except Exception as e:
            self.log(f"❌ Erreur open_dropdown_and_select: {e}")
            return False

    def send_tab(self, count=1):
        """Envoie la touche Tab un certain nombre de fois"""
        try:
            self.log(f"   ⌨️  Envoi de {count} Tab...")
            for _ in range(count):
                self.keyboard.press(Key.tab)
                self.keyboard.release(Key.tab)
                time.sleep(0.2)
            self.log(f"   ✅ {count} Tab envoyé(s)")
            return True
        except Exception as e:
            self.log(f"   ❌ Erreur envoi Tab: {e}")
            return False

    def get_column_b_navigation(self, code_b):
        """Détermine les touches de navigation pour le code B"""
        if not code_b:
            return []

        code_upper = code_b.upper()

        # Mapping des codes B vers les touches de navigation
        if code_upper == "TIERS":
            return ['t']  # V puis T pour TIERS
        elif code_upper == "OI":
            return ['o']  # V puis O une fois pour OI
        elif code_upper == "OC":
            return ['o', 'o']  # V puis O deux fois pour OC
        else:
            # Pour d'autres codes, essayer la première lettre
            return [code_upper[0].lower()] if code_upper else []

    def get_column_c_navigation(self, code_c):
        """Détermine les touches de navigation pour le code C"""
        if not code_c:
            return []

        code_upper = code_c.upper()

        # Mapping des codes C vers les touches de navigation
        # Ordre dans le menu: PBO (1x P), PM (2x P), PM-PBO (3x P), PTO (4x P), RACCO PALIER (R), HORIZONTALE RESEAU (H)
        mappings = {
            "PBO": ['p'],  # V puis P une fois pour PBO (1ère position P)
            "PM": ['p', 'p'],  # V puis P deux fois pour PM (2ème position P)
            "PM-PBO": ['p', 'p', 'p'],  # V puis P trois fois pour PM-PBO (3ème position P)
            "PTO": ['p', 'p', 'p', 'p'],  # V puis P quatre fois pour PTO (4ème position P)
            "RACCO PALIER": ['r'],  # V puis R pour RACCO PALIER
            "HORIZONTALE RESEAU": ['h'],  # V puis H pour HORIZONTALE RESEAU
            # Anciens mappings pour compatibilité
            "PRDM": ['p', 'r'],  # V puis P puis R pour PRDM
            "PM-PRDM": ['p', 'm'],  # V puis P puis M pour PM-PRDM
            "BRAM": ['b']  # V puis B pour BRAM
        }

        return mappings.get(code_upper, [code_upper[0].lower()] if code_upper else [])



    def process_column_b(self, code_b):
        """Traite la logique de la colonne B avec menu déroulant"""
        if not code_b:
            self.log("   📝 Colonne B vide → Pas d'action")
            return True

        self.log(f"   🔍 Traitement colonne B: {code_b}")
        navigation_keys = self.get_column_b_navigation(code_b)

        if not navigation_keys:
            self.log(f"   ⚠️  Aucune correspondance trouvée pour '{code_b}'")
            return True

        self.log(f"   🔽 V puis navigation: {navigation_keys}")

        # Utiliser la nouvelle méthode avec menu déroulant
        if self.open_dropdown_and_select(code_b, navigation_keys):
            self.log(f"   ✅ '{code_b}' sélectionné avec succès!")
            return True
        else:
            self.log(f"   ⚠️  Sélection '{code_b}' échouée")
            return False

    def process_column_c(self, code_c):
        """Traite la logique de la colonne C avec menu déroulant"""
        if not code_c:
            self.log("   📝 Colonne C vide → Pas d'action")
            return True

        self.log(f"   🔍 Traitement colonne C: {code_c}")
        navigation_keys = self.get_column_c_navigation(code_c)

        if not navigation_keys:
            self.log(f"   ⚠️  Aucune correspondance trouvée pour '{code_c}'")
            return True

        self.log(f"   🔽 V puis navigation: {navigation_keys}")

        # Utiliser la nouvelle méthode avec menu déroulant
        if self.open_dropdown_and_select(code_c, navigation_keys):
            self.log(f"   ✅ '{code_c}' sélectionné avec succès!")
            return True
        else:
            self.log(f"   ⚠️  Sélection '{code_c}' échouée")
            return False

    def get_column_d_repeat_count(self, code_d):
        """Détermine le nombre de répétitions de la première lettre basé sur le chiffre dans code_d"""
        if not code_d or len(code_d) < 4:
            return 1  # Par défaut 1 répétition

        try:
            # Extraire seulement les chiffres après les 3 premières lettres
            # Format: RET02#PRELOC ERRONEE → on veut juste "02"
            # Prendre les caractères 3 et 4 (positions 3-4) qui sont les chiffres
            if len(code_d) >= 5:
                number_part = code_d[3:5]  # Prendre exactement 2 caractères (les chiffres)
            else:
                number_part = code_d[3:]  # Prendre ce qui reste si moins de 5 caractères

            # Convertir en entier (enlève les zéros de tête automatiquement)
            repeat_count = int(number_part)
            return max(1, repeat_count)  # Au minimum 1 répétition
        except (ValueError, IndexError):
            return 1  # Par défaut si erreur de parsing

    def process_column_d_final(self, code_d):
        """Traite la logique finale de la colonne D basée sur le chiffre"""
        if not code_d:
            self.log("   📝 Colonne D vide → Pas d'action finale")
            return True

        self.log(f"   🔍 Traitement final colonne D: '{code_d}'")

        # Extraire la première lettre et le nombre de répétitions
        first_letter = code_d[0].lower() if code_d else 'r'
        repeat_count = self.get_column_d_repeat_count(code_d)

        self.log(f"   📝 Code D analysé: '{code_d}'")
        self.log(f"   📝 Première lettre extraite: '{first_letter.upper()}'")
        self.log(f"   📝 Nombre de répétitions calculé: {repeat_count}")

        # Créer la liste de navigation (répéter la première lettre)
        navigation_keys = [first_letter] * repeat_count

        self.log(f"   🔽 V puis navigation: {navigation_keys}")

        # Utiliser la méthode avec menu déroulant
        if self.open_dropdown_and_select(f"Code {first_letter.upper()} x{repeat_count}", navigation_keys):
            self.log(f"   ✅ Code D final sélectionné avec succès!")
            return True
        else:
            self.log(f"   ⚠️  Sélection code D final échouée")
            return False

    def process_single_case(self, case_data):
        """Traite un seul cas"""
        try:
            case_id = case_data['id']
            code_b = case_data['code_b']
            code_c = case_data['code_c']
            code_d = case_data['code_d']
            code_e = case_data['code_e']
            # ÉTAPE 1: Ctrl+I
            self.log("   🔄 Ctrl+I...")
            with self.keyboard.pressed(Key.ctrl):
                self.keyboard.press('i')
                self.keyboard.release('i')
            time.sleep(1.0)  # 0.5s → 1.0s (doublé)

            # ÉTAPE 2: Saisir l'ID
            self.log(f"   ⌨️  Saisie: {case_id}")
            self.keyboard.type(case_id)
            time.sleep(1.0)  # 0.5s → 1.0s (doublé)

            # ÉTAPE 3: Entrée
            self.log("   ⏎ Entrée (recherche)...")
            self.keyboard.press(Key.enter)
            self.keyboard.release(Key.enter)
            time.sleep(1.0)

            # Attendre 6 secondes pour laisser le cas s'ouvrir complètement (avec vérification d'arrêt)
            self.log("   ⏳ Attente 6 secondes pour ouverture du cas...")
            for i in range(6):
                if not self.is_running:
                    return
                time.sleep(1.0)

            # ÉTAPE 4: Ctrl+Shift+Y (prendre le cas en force) - avec forçage de focus
            self.log("   🔒 Prise en force du cas...")
            success = False

            for attempt in range(1, 4):
                if not self.is_running:  # Vérifier si arrêt demandé
                    return

                self.log(f"   🎯 Tentative {attempt}/3 de prise en force...")

                # Forcer le focus avant chaque tentative
                try:
                    win32gui.SetForegroundWindow(clarify_hwnd)
                    time.sleep(0.5)
                    self.log("      🎯 Focus Clarify forcé")
                except Exception as e:
                    self.log(f"      ⚠️  Erreur focus: {e}")

                # Utiliser la méthode qui fonctionne
                try:
                    self.log("      ⌨️  Envoi de Ctrl+Shift+Y...")
                    with self.keyboard.pressed(Key.ctrl):
                        with self.keyboard.pressed(Key.shift):
                            self.keyboard.press('y')
                            self.keyboard.release('y')
                    time.sleep(1.0)
                    self.log(f"   ✅ Ctrl+Shift+Y réussi (tentative {attempt})!")
                    success = True
                    break
                except Exception as e:
                    self.log(f"   ❌ Ctrl+Shift+Y échoué (tentative {attempt}): {e}")
                    if attempt < 3:
                        time.sleep(1)

            if not success:
                self.log("   ❌ Échec définitif Ctrl+Shift+Y après 3 tentatives")
                return

            # ÉTAPE 5: Entrée (après Ctrl+Shift+Y)
            self.log("   ⏎ Entrée (confirmation)...")
            self.keyboard.press(Key.enter)
            self.keyboard.release(Key.enter)
            time.sleep(1.0)  # 0.5s → 1.0s (doublé)

            # ÉTAPE 6: Attente 1 seconde
            self.log("   ⏳ Attente 1 seconde...")
            if not self.is_running:
                return
            time.sleep(1.0)

            # ÉTAPE 7: Entrée finale
            self.log("   ⏎ Entrée (finale)...")
            self.keyboard.press(Key.enter)
            self.keyboard.release(Key.enter)
            time.sleep(1.0)  # 0.5s → 1.0s (doublé)

            # ÉTAPE 7.5: Attente supplémentaire de 8 secondes après Entrée finale
            self.log("   ⏳ Attente supplémentaire de 8 secondes après Entrée finale...")
            for i in range(8):
                if not self.is_running:
                    return
                time.sleep(1.0)

            # ÉTAPE 8: Attente 8-10 secondes (avec vérification d'arrêt)
            wait_time = random.randint(8, 10)
            self.log(f"   ⏳ Attente {wait_time} secondes avant clic sur Suivi Résolution...")
            for i in range(wait_time):
                if not self.is_running:
                    return
                time.sleep(1.0)

            # ÉTAPE 9: Navigation avec Ctrl+Tab au lieu de clic sur image
            self.log("   🎯 Navigation Ctrl+Tab vers Suivi Résolution...")

            # Maintenir Ctrl et faire 7x Tab
            self.log("   ⌨️  Ctrl maintenu + 7x Tab...")
            try:
                with self.keyboard.pressed(Key.ctrl):
                    for _ in range(7):
                        self.keyboard.press(Key.tab)
                        self.keyboard.release(Key.tab)
                        time.sleep(0.2)  # Petite pause entre chaque Tab
                self.log("   ✅ Ctrl+7xTab terminé!")

                # Attendre 8 secondes
                self.log("   ⏳ Attente 8 secondes...")
                time.sleep(8)
                self.log("   ✅ Navigation vers Suivi Résolution terminée!")

            except Exception as e:
                self.log(f"   ❌ Erreur navigation Ctrl+Tab: {e}")
                return False

            # ÉTAPE 10: Logique conditionnelle basée sur la colonne D
            if code_d:
                self.log(f"   🔍 Analyse du code D: {code_d}")

                # Extraire le préfixe du code (ERR, RET, STT, ABS, EXC)
                code_upper = code_d.upper()

                if code_upper.startswith("RET"):
                    self.log("   📝 Code RET → V puis R R pour 'Réussi'")
                    if self.open_dropdown_and_select("Réussi", ['r', 'r']):
                        self.log("   ✅ 'Réussi' sélectionné avec succès!")
                    else:
                        self.log("   ⚠️  Sélection 'Réussi' échouée")

                elif code_upper.startswith("ERR"):
                    self.log("   📝 Code ERR → V puis E pour 'Erreur'")
                    if self.open_dropdown_and_select("Erreur", ['e']):
                        self.log("   ✅ 'Erreur' sélectionné avec succès!")
                    else:
                        self.log("   ⚠️  Sélection 'Erreur' échouée")

                elif code_upper.startswith("STT"):
                    self.log("   📝 Code STT → V puis T pour 'Transmis à tort'")
                    if self.open_dropdown_and_select("Transmis à tort", ['t']):
                        self.log("   ✅ 'Transmis à tort' sélectionné avec succès!")
                        # Délai supplémentaire pour STT car position différente dans le menu
                        time.sleep(0.5)
                    else:
                        self.log("   ⚠️  Sélection 'Transmis à tort' échouée")

                elif code_upper.startswith("ABS"):
                    self.log("   📝 Code ABS → V puis T pour 'Transmis à tort'")
                    if self.open_dropdown_and_select("Transmis à tort", ['t']):
                        self.log("   ✅ 'Transmis à tort' sélectionné avec succès!")
                    else:
                        self.log("   ⚠️  Sélection 'Transmis à tort' échouée")

                elif code_upper.startswith("EXC"):
                    self.log("   📝 Code EXC → V puis T pour 'Transmis à tort'")
                    if self.open_dropdown_and_select("Transmis à tort", ['t']):
                        self.log("   ✅ 'Transmis à tort' sélectionné avec succès!")
                    else:
                        self.log("   ⚠️  Sélection 'Transmis à tort' échouée")

                else:
                    self.log(f"   📝 Code non reconnu '{code_d}' → V puis première lettre")
                    first_letter = code_upper[0].lower() if code_upper else 't'
                    if self.open_dropdown_and_select(f"Code {first_letter.upper()}", [first_letter]):
                        self.log(f"   ✅ 'Code {first_letter.upper()}' sélectionné avec succès!")
                    else:
                        self.log(f"   ⚠️  Sélection 'Code {first_letter.upper()}' échouée")
            else:
                self.log("   📝 Aucun code D → Pas d'action supplémentaire")

            # NOUVELLE ÉTAPE: 1x Tab après sélection D (identique pour tous les codes)
            self.log("   ⌨️  1x Tab après sélection D...")
            if not self.send_tab(1):
                self.log("   ❌ Échec envoi 1x Tab")
                return False

            # ÉTAPE 6: 1x Tab → Shift+PageDown → Suppr → Colonne E → 1x Tab → Barre espace → Attendre 0.5s → 4x Tab
            if code_e:
                self.log(f"   🎯 Étape 6: 1x Tab → Shift+PageDown → Suppr → Colonne E → 1x Tab → Barre espace → Attendre 0.5s → 4x Tab")
                try:
                    # Shift+PageDown
                    with self.keyboard.pressed(Key.shift):
                        self.keyboard.press(Key.page_down)
                        self.keyboard.release(Key.page_down)
                    time.sleep(0.25)

                    # Suppr
                    self.keyboard.press(Key.delete)
                    self.keyboard.release(Key.delete)
                    time.sleep(0.25)

                    # Saisir le texte de la colonne E
                    self.keyboard.type(code_e)
                    self.log(f"   ✅ Colonne E saisie: '{code_e}'")
                    time.sleep(0.25)

                    # 1x Tab
                    self.log("   ⌨️  1x Tab...")
                    if not self.send_tab(1):
                        self.log("   ❌ Échec envoi 1x Tab")
                        return False
                    time.sleep(0.25)

                    # Barre espace
                    self.log("   ⌨️  Barre espace...")
                    self.keyboard.press(Key.space)
                    self.keyboard.release(Key.space)

                    # Attendre 0.5s
                    self.log("   ⏳ Attente 0.5 seconde...")
                    time.sleep(0.5)

                    # 4x Tab
                    self.log("   ⌨️  4x Tab...")
                    if not self.send_tab(4):
                        self.log("   ❌ Échec envoi 4x Tab")
                        return False

                    # 1x Tab supplémentaire (correction spécifique pour STT)
                    if code_d and code_d.upper().startswith("STT"):
                        self.log("   ⌨️  1x Tab supplémentaire (STT uniquement)...")
                        if not self.send_tab(1):
                            self.log("   ❌ Échec envoi 1x Tab supplémentaire STT")
                            return False

                except Exception as e:
                    self.log(f"   ❌ Erreur Étape 6: {e}")
                    return False
            else:
                self.log("   📝 Colonne E vide → Étape 6 simplifiée: 1x Tab → Barre espace → Attendre 0.5s → 4x Tab")
                try:
                    # 1x Tab
                    if not self.send_tab(1):
                        self.log("   ❌ Échec envoi 1x Tab")
                        return False
                    time.sleep(0.25)

                    # Barre espace
                    self.keyboard.press(Key.space)
                    self.keyboard.release(Key.space)

                    # Attendre 0.5s
                    self.log("   ⏳ Attente 0.5 seconde...")
                    time.sleep(0.5)

                    # 4x Tab
                    if not self.send_tab(4):
                        self.log("   ❌ Échec envoi 4x Tab")
                        return False

                    # 1x Tab supplémentaire (correction spécifique pour STT)
                    if code_d and code_d.upper().startswith("STT"):
                        self.log("   ⌨️  1x Tab supplémentaire (STT uniquement)...")
                        if not self.send_tab(1):
                            self.log("   ❌ Échec envoi 1x Tab supplémentaire STT")
                            return False

                except Exception as e:
                    self.log(f"   ❌ Erreur Étape 6 simplifiée: {e}")
                    return False

            # Attendre 0.25 seconde après les 4 tabs (0.5s / 2)
            self.log("   ⏳ Attente 0.25 seconde...")
            time.sleep(0.25)

            # ÉTAPE 12: Traitement colonne B
            if not self.process_column_b(code_b):
                self.log("   ⚠️  Traitement colonne B échoué, on continue...")

            # Attendre 0.25 seconde après colonne B (0.5s / 2)
            self.log("   ⏳ Attente 0.25 seconde après colonne B...")
            time.sleep(0.25)

            # ÉTAPE 13: Tab pour passer au champ suivant
            self.log("   ⌨️  Tab pour champ suivant...")
            if not self.send_tab(1):
                self.log("   ❌ Échec envoi Tab")
                return False

            # Attendre 0.25 seconde après le tab (0.5s / 2)
            self.log("   ⏳ Attente 0.25 seconde...")
            time.sleep(0.25)

            # ÉTAPE 14: Traitement colonne C
            if not self.process_column_c(code_c):
                self.log("   ⚠️  Traitement colonne C échoué, on continue...")

            # Attendre 0.25 seconde après colonne C (0.5s / 2)
            self.log("   ⏳ Attente 0.25 seconde après colonne C...")
            time.sleep(0.25)

            # ÉTAPE 15: Tab pour passer au champ final (colonne D)
            self.log("   ⌨️  Tab pour champ final (colonne D)...")
            if not self.send_tab(1):
                self.log("   ❌ Échec envoi Tab final")
                return False

            # Attendre 0.25 seconde après le tab final (0.5s / 2)
            self.log("   ⏳ Attente 0.25 seconde...")
            time.sleep(0.25)

            # ÉTAPE 16: Traitement final colonne D (basé sur le chiffre)
            if not self.process_column_d_final(code_d):
                self.log("   ⚠️  Traitement final colonne D échoué, on continue...")

            # ÉTAPE 17: Shift maintenu + 4x Tab puis Espace deux fois
            self.log("   🎯 ÉTAPE FINALE: Shift maintenu + 4x Tab puis Espace deux fois")
            try:
                # Maintenir Shift et faire 4x Tab
                self.log("   ⌨️  Shift maintenu + 4x Tab...")
                self.keyboard.press(Key.shift)
                for i in range(4):
                    self.keyboard.press(Key.tab)
                    self.keyboard.release(Key.tab)
                    time.sleep(0.25)
                    self.log(f"   ⌨️  Tab {i+1}/4 (Shift maintenu)")
                self.keyboard.release(Key.shift)
                self.log("   ✅ Shift relâché")

                # Espace deux fois
                self.log("   ⌨️  Espace 1/2...")
                self.keyboard.press(Key.space)
                self.keyboard.release(Key.space)
                time.sleep(0.25)

                self.log("   ⌨️  Espace 2/2...")
                self.keyboard.press(Key.space)
                self.keyboard.release(Key.space)
                time.sleep(0.25)

                # Attente 14 secondes puis Ctrl+W
                self.log("   ⏳ Attente 14 secondes...")
                time.sleep(14.0)

                self.log("   ⌨️  Ctrl+W...")
                with self.keyboard.pressed(Key.ctrl):
                    self.keyboard.press('w')
                    self.keyboard.release('w')
                time.sleep(1.0)

                # Focus sur Clarify pour préparer le cas suivant
                self.log("   🎯 Focus sur Clarify pour le cas suivant...")
                try:
                    import win32gui
                    windows = self.find_clarify_window()
                    if windows:
                        hwnd = windows[0][0]
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(0.5)
                        self.log("   ✅ Focus Clarify restauré")
                    else:
                        self.log("   ⚠️  Clarify non trouvé pour focus")
                except Exception as e:
                    self.log(f"   ⚠️  Erreur focus Clarify: {e}")

                self.log("   ✅ Étape finale terminée avec succès!")

            except Exception as e:
                self.log(f"   ❌ Erreur étape finale: {e}")
                # S'assurer que Shift est relâché en cas d'erreur
                try:
                    self.keyboard.release(Key.shift)
                except:
                    pass

            return True

        except Exception as e:
            self.log(f"❌ Erreur traitement {case_id}: {e}")
            return False

    def process_tt_only_case(self, case_data):
        """Traite un cas TT uniquement (Ouvrir/Accepter seulement - Étapes 1-8.5 puis Ctrl+W)"""
        try:
            case_id = case_data['id']

            # ÉTAPE 1: Ctrl+I
            self.log("   🔄 Ctrl+I...")
            with self.keyboard.pressed(Key.ctrl):
                self.keyboard.press('i')
                self.keyboard.release('i')
            time.sleep(1.0)

            # ÉTAPE 2: Saisir l'ID
            self.log(f"   ⌨️  Saisie: {case_id}")
            self.keyboard.type(case_id)
            time.sleep(1.0)

            # ÉTAPE 3: Entrée
            self.log("   ⏎ Entrée (recherche)...")
            self.keyboard.press(Key.enter)
            self.keyboard.release(Key.enter)
            time.sleep(1.0)

            # ÉTAPE 4: Attendre 6 secondes pour laisser le cas s'ouvrir complètement
            self.log("   ⏳ Attente 6 secondes pour ouverture du cas...")
            for i in range(6):
                if not self.is_running:
                    return False
                time.sleep(1.0)

            # ÉTAPE 5: Ctrl+Shift+Y (prendre le cas en force)
            self.log("   🔒 Prise en force du cas...")
            success = False

            for attempt in range(1, 4):
                if not self.is_running:
                    return False

                self.log(f"   🎯 Tentative {attempt}/3 de prise en force...")

                # Forcer le focus avant chaque tentative
                try:
                    windows = self.find_clarify_window()
                    if windows:
                        hwnd = windows[0][0]
                        import win32gui
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(0.5)
                        self.log("      🎯 Focus Clarify forcé")
                except Exception as e:
                    self.log(f"      ⚠️  Erreur focus: {e}")

                # Utiliser la méthode qui fonctionne
                try:
                    self.log("      ⌨️  Envoi de Ctrl+Shift+Y...")
                    with self.keyboard.pressed(Key.ctrl):
                        with self.keyboard.pressed(Key.shift):
                            self.keyboard.press('y')
                            self.keyboard.release('y')
                    time.sleep(1.0)
                    self.log(f"   ✅ Ctrl+Shift+Y réussi (tentative {attempt})!")
                    success = True
                    break
                except Exception as e:
                    self.log(f"   ❌ Ctrl+Shift+Y échoué (tentative {attempt}): {e}")
                    if attempt < 3:
                        time.sleep(1)

            if not success:
                self.log("   ❌ Échec définitif Ctrl+Shift+Y après 3 tentatives")
                return False

            # ÉTAPE 6: Entrée (après Ctrl+Shift+Y)
            self.log("   ⏎ Entrée (confirmation)...")
            self.keyboard.press(Key.enter)
            self.keyboard.release(Key.enter)
            time.sleep(1.0)

            # ÉTAPE 7: Attente 1 seconde
            self.log("   ⏳ Attente 1 seconde...")
            if not self.is_running:
                return False
            time.sleep(1.0)

            # ÉTAPE 8: Entrée finale
            self.log("   ⏎ Entrée (finale)...")
            self.keyboard.press(Key.enter)
            self.keyboard.release(Key.enter)
            time.sleep(1.0)

            # ÉTAPE 8.5: Attente supplémentaire de 8 secondes après Entrée finale
            self.log("   ⏳ Attente supplémentaire de 8 secondes après Entrée finale...")
            for i in range(8):
                if not self.is_running:
                    return False
                time.sleep(1.0)

            # FERMETURE: Ctrl+W directement (pas de saisie de données)
            self.log("   ⌨️  Ctrl+W (fermeture TT uniquement)...")
            with self.keyboard.pressed(Key.ctrl):
                self.keyboard.press('w')
                self.keyboard.release('w')
            time.sleep(1.0)

            # ÉTAPE 20: Focus sur Clarify pour préparer le cas suivant
            self.log("   🎯 Focus sur Clarify pour le cas suivant...")
            try:
                import win32gui
                windows = self.find_clarify_window()
                if windows:
                    hwnd = windows[0][0]
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(0.5)
                    self.log("   ✅ Focus Clarify restauré")
                else:
                    self.log("   ⚠️  Clarify non trouvé pour focus")
            except Exception as e:
                self.log(f"   ⚠️  Erreur focus Clarify: {e}")

            self.log("   ✅ TT uniquement terminé avec succès!")
            return True

        except Exception as e:
            self.log(f"❌ Erreur traitement TT uniquement {case_id}: {e}")
            return False

def main():
    """Fonction principale"""
    root = tk.Tk()
    app = ClarifyGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
