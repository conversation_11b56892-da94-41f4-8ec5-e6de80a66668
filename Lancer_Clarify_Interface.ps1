# Script PowerShell pour lancer l'interface Clarify
# Utilisation: .\Lancer_Clarify_Interface.ps1

Write-Host ""
Write-Host "🚀 CLARIFY - INTERFACE DE TRAITEMENT AUTOMATIQUE" -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Green
Write-Host ""

# Afficher le répertoire courant
Write-Host "📁 Répertoire courant: $PWD" -ForegroundColor Cyan
Write-Host ""

# Vérifier si le fichier lancer_interface.py existe
if (-not (Test-Path "lancer_interface.py")) {
    Write-Host "❌ Le fichier lancer_interface.py n'existe pas dans ce répertoire" -ForegroundColor Red
    Write-Host "📁 Contenu du répertoire:" -ForegroundColor Yellow
    Get-ChildItem -Name "*.py"
    Write-Host ""
    Write-Host "💡 Assurez-vous d'être dans le bon répertoire" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

Write-Host "✅ Fichier lancer_interface.py trouvé" -ForegroundColor Green
Write-Host ""

# Vérifier si Python est installé
Write-Host "🔍 Vérification de Python..." -ForegroundColor Cyan

$pythonCmd = $null

# Tester python
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python détecté: $pythonVersion" -ForegroundColor Green
        $pythonCmd = "python"
    }
} catch {
    # Python pas trouvé
}

# Si python ne fonctionne pas, tester py
if (-not $pythonCmd) {
    try {
        $pyVersion = py --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ py.exe détecté: $pyVersion" -ForegroundColor Green
            $pythonCmd = "py"
        }
    } catch {
        # py pas trouvé non plus
    }
}

# Si aucun Python trouvé
if (-not $pythonCmd) {
    Write-Host "❌ Python n'est pas installé ou pas dans le PATH" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Installez Python depuis https://python.org" -ForegroundColor Yellow
    Write-Host "   Assurez-vous de cocher 'Add Python to PATH' lors de l'installation" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Appuyez sur Entrée pour quitter"
    exit 1
}

Write-Host ""

# Lancer l'interface
Write-Host "🎯 Lancement de l'interface avec $pythonCmd..." -ForegroundColor Cyan
Write-Host ""

try {
    & $pythonCmd "lancer_interface.py"
    $exitCode = $LASTEXITCODE
} catch {
    Write-Host "❌ Erreur lors du lancement: $_" -ForegroundColor Red
    $exitCode = 1
}

# Si on arrive ici, c'est que le script s'est terminé
Write-Host ""
Write-Host "📋 L'interface s'est fermée" -ForegroundColor Yellow
Write-Host "🔍 Code de sortie: $exitCode" -ForegroundColor Cyan

if ($exitCode -ne 0) {
    Write-Host "⚠️  Le script s'est terminé avec des erreurs" -ForegroundColor Yellow
}

Read-Host "Appuyez sur Entrée pour quitter"
