import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import sys
import os
import subprocess
import queue
import time
from datetime import datetime
import pandas as pd
from backup_manager import BackupManager

# Import des modules du script principal
import win32gui
import win32ui
import win32con
from ctypes import windll
import pyautogui
from PIL import Image
import cv2
import numpy as np

# Import du sélecteur d'icône
try:
    from Selector_icone import IconSelector
    ICON_SELECTOR_AVAILABLE = True
except ImportError:
    print("⚠️ Selector_icone.py non trouvé - Fonctionnalité de sélection d'icône désactivée")
    ICON_SELECTOR_AVAILABLE = False

class ClarifyGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Clarify - Interface de Traitement Automatique")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # Variables
        self.excel_file_path = tk.StringVar()
        self.is_running = False
        self.process_thread = None
        self.stop_requested = False
        self.output_queue = queue.Queue()

        # Gestionnaire de sauvegarde
        self.backup_manager = BackupManager()
        
        # Configuration du style
        self.setup_styles()
        
        # Création de l'interface
        self.create_widgets()
        
        # Démarrer la vérification de la queue de sortie
        self.check_output_queue()
        
    def setup_styles(self):
        """Configure les styles de l'interface"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Style pour les boutons
        style.configure('Start.TButton', 
                       background='#4CAF50', 
                       foreground='white',
                       font=('Arial', 10, 'bold'))
        style.configure('Stop.TButton', 
                       background='#f44336', 
                       foreground='white',
                       font=('Arial', 10, 'bold'))
        style.configure('Browse.TButton', 
                       background='#2196F3', 
                       foreground='white',
                       font=('Arial', 9))
        
    def create_widgets(self):
        """Crée tous les widgets de l'interface"""
        
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du redimensionnement
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # Titre
        title_label = ttk.Label(main_frame, 
                               text="🚀 Clarify - Traitement Automatique", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Section sélection de fichier
        file_frame = ttk.LabelFrame(main_frame, text="📁 Fichier Excel", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="Fichier TT_liste.xlsx:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.file_entry = ttk.Entry(file_frame, textvariable=self.excel_file_path, width=50)
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        self.browse_button = ttk.Button(file_frame, text="Parcourir", 
                                       command=self.browse_file, style='Browse.TButton')
        self.browse_button.grid(row=0, column=2)
        
        # Section contrôles
        control_frame = ttk.LabelFrame(main_frame, text="🎮 Contrôles", padding="10")
        control_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_button = ttk.Button(control_frame, text="▶️ START", 
                                      command=self.start_processing, style='Start.TButton')
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="⏹️ STOP",
                                     command=self.stop_processing, style='Stop.TButton',
                                     state='disabled')
        self.stop_button.grid(row=0, column=1, padx=(0, 10))

        # Bouton sélecteur d'icône
        if ICON_SELECTOR_AVAILABLE:
            self.icon_selector_button = ttk.Button(control_frame, text="🎯 Sélecteur d'icône",
                                                  command=self.open_icon_selector)
            self.icon_selector_button.grid(row=0, column=2, padx=(0, 20))
            status_column = 3
        else:
            status_column = 2

        # Indicateur de statut
        self.status_label = ttk.Label(control_frame, text="⏸️ Arrêté",
                                     font=('Arial', 10, 'bold'))
        self.status_label.grid(row=0, column=status_column, padx=(20, 0))
        
        # Section console
        console_frame = ttk.LabelFrame(main_frame, text="📟 Console de Traitement", padding="10")
        console_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        console_frame.columnconfigure(0, weight=1)
        console_frame.rowconfigure(0, weight=1)
        
        # Zone de texte avec scrollbar
        self.console_text = scrolledtext.ScrolledText(console_frame, 
                                                     width=80, height=25,
                                                     font=('Consolas', 9),
                                                     bg='#1e1e1e', fg='#ffffff',
                                                     insertbackground='white')
        self.console_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Bouton pour effacer la console
        clear_button = ttk.Button(console_frame, text="🗑️ Effacer Console", 
                                 command=self.clear_console)
        clear_button.grid(row=1, column=0, pady=(10, 0))
        
        # Section informations
        info_frame = ttk.LabelFrame(main_frame, text="ℹ️ Informations", padding="10")
        info_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E))
        
        info_text = ("💡 Instructions:\n"
                    "1. Sélectionnez le fichier TT_liste.xlsx contenant les IDs à traiter\n"
                    "2. Assurez-vous que Clarify est ouvert\n"
                    "3. Cliquez sur START pour commencer le traitement\n"
                    "4. Utilisez STOP pour arrêter à tout moment\n"
                    "5. Le suivi est automatiquement sauvé dans Clarify_Suivi_Traitement.xlsx")
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.grid(row=0, column=0, sticky=tk.W)
        
        # Initialiser avec le fichier par défaut s'il existe
        default_file = "TT_liste.xlsx"
        if os.path.exists(default_file):
            self.excel_file_path.set(os.path.abspath(default_file))
            self.log_to_console(f"✅ Fichier par défaut trouvé: {default_file}")
        
    def browse_file(self):
        """Ouvre la boîte de dialogue pour sélectionner le fichier Excel"""
        file_path = filedialog.askopenfilename(
            title="Sélectionner le fichier TT_liste.xlsx",
            filetypes=[("Fichiers Excel", "*.xlsx"), ("Tous les fichiers", "*.*")],
            initialdir=os.getcwd()
        )
        
        if file_path:
            self.excel_file_path.set(file_path)
            self.log_to_console(f"📁 Fichier sélectionné: {os.path.basename(file_path)}")

    def open_icon_selector(self):
        """Ouvre le sélecteur d'icône"""
        if not ICON_SELECTOR_AVAILABLE:
            messagebox.showerror("Erreur",
                               "Le sélecteur d'icône n'est pas disponible.\n"
                               "Assurez-vous que le fichier Selector_icone.py est présent.")
            return

        try:
            self.log_to_console("🎯 Ouverture du sélecteur d'icône...")

            # Créer le sélecteur avec parent pour éviter les conflits
            selector = IconSelector(parent=self.root)

            # Centrer la fenêtre par rapport à la fenêtre principale
            self.center_window(selector.root)

            # Attendre que la fenêtre se ferme
            self.root.wait_window(selector.root)

            self.log_to_console("✅ Sélecteur d'icône fermé")

            # Vérifier si un nouveau template a été créé
            if os.path.exists("search_icon_selected.png"):
                self.log_to_console("🎉 Nouveau template d'icône détecté!")

                # Optionnel: proposer de tester le template
                if messagebox.askyesno("Template créé",
                                     "Un nouveau template d'icône a été créé.\n"
                                     "Voulez-vous tester sa détection maintenant?"):
                    self.test_icon_template()

        except Exception as e:
            self.log_to_console(f"❌ Erreur lors de l'ouverture du sélecteur: {e}")
            messagebox.showerror("Erreur", f"Impossible d'ouvrir le sélecteur d'icône:\n{e}")

    def center_window(self, window):
        """Centre une fenêtre par rapport à la fenêtre principale"""
        try:
            # Obtenir les dimensions de la fenêtre principale
            main_x = self.root.winfo_x()
            main_y = self.root.winfo_y()
            main_width = self.root.winfo_width()
            main_height = self.root.winfo_height()

            # Forcer la mise à jour de la géométrie de la nouvelle fenêtre
            window.update_idletasks()

            # Obtenir les dimensions de la nouvelle fenêtre
            window_width = window.winfo_reqwidth()
            window_height = window.winfo_reqheight()

            # Calculer la position centrée
            x = main_x + (main_width - window_width) // 2
            y = main_y + (main_height - window_height) // 2

            # Appliquer la position
            window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        except Exception as e:
            print(f"Erreur centrage fenêtre: {e}")

    def test_icon_template(self):
        """Teste le template d'icône actuel"""
        try:
            self.log_to_console("🧪 Test du template d'icône...")

            # Vérifier que Clarify est ouvert
            clarify_windows = self.find_clarify_window()
            if not clarify_windows:
                messagebox.showwarning("Attention",
                                     "Aucune fenêtre Clarify trouvée.\n"
                                     "Ouvrez Clarify pour tester le template.")
                return

            # Prendre une capture d'écran de Clarify
            hwnd, title = clarify_windows[0]
            screenshot_path = self.capture_clarify_window(hwnd)

            if screenshot_path and os.path.exists("search_icon_selected.png"):
                # Tester la détection
                screenshot = cv2.imread(screenshot_path)
                template = cv2.imread("search_icon_selected.png")

                if screenshot is not None and template is not None:
                    screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
                    template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

                    result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                    if max_val > 0.7:
                        self.log_to_console(f"✅ Template testé avec succès! Score: {max_val:.3f}")
                        messagebox.showinfo("Test réussi",
                                          f"Le template fonctionne bien!\n"
                                          f"Score de détection: {max_val:.3f}")
                    else:
                        self.log_to_console(f"⚠️ Template faible. Score: {max_val:.3f}")
                        messagebox.showwarning("Test faible",
                                             f"Le template a un score faible: {max_val:.3f}\n"
                                             f"Vous devriez peut-être le recréer.")
                else:
                    self.log_to_console("❌ Impossible de charger les images pour le test")
            else:
                self.log_to_console("❌ Impossible de capturer Clarify ou template manquant")

        except Exception as e:
            self.log_to_console(f"❌ Erreur lors du test: {e}")
            messagebox.showerror("Erreur", f"Erreur lors du test du template:\n{e}")

    def find_clarify_window(self):
        """Trouve les fenêtres Clarify (méthode utilitaire)"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if "Amdocs CRM - ClearSupport" in window_text or "CLARIFY" in window_text:
                    windows.append((hwnd, window_text))
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        return windows

    def capture_clarify_window(self, hwnd):
        """Capture une fenêtre Clarify (méthode utilitaire)"""
        try:
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()

            rect = win32gui.GetWindowRect(hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]

            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)

            result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 3)

            if result:
                bmpinfo = saveBitMap.GetInfo()
                bmpstr = saveBitMap.GetBitmapBits(True)
                img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                                     bmpstr, 'raw', 'BGRX', 0, 1)

                screenshot_path = "clarify_test_capture.png"
                img.save(screenshot_path)

                # Nettoyage
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)

                return screenshot_path
            else:
                return None

        except Exception as e:
            print(f"Erreur capture: {e}")
            return None

    def log_to_console(self, message):
        """Ajoute un message à la console avec timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        # Utiliser la queue pour thread-safety
        self.output_queue.put(formatted_message)
        
    def check_output_queue(self):
        """Vérifie la queue de sortie et met à jour la console"""
        try:
            while True:
                message = self.output_queue.get_nowait()
                self.console_text.insert(tk.END, message)
                self.console_text.see(tk.END)
        except queue.Empty:
            pass
        
        # Programmer la prochaine vérification
        self.root.after(100, self.check_output_queue)
        
    def clear_console(self):
        """Efface le contenu de la console"""
        self.console_text.delete(1.0, tk.END)
        self.log_to_console("🗑️ Console effacée")
        
    def start_processing(self):
        """Démarre le traitement"""

        # Vérifications préalables
        if not self.excel_file_path.get():
            messagebox.showerror("Erreur", "Veuillez sélectionner un fichier Excel")
            return

        # Créer une sauvegarde de session
        try:
            self.log_to_console("💾 Création des sauvegardes de session...")
            self.backup_manager.create_session_backup()
            self.backup_manager.start_auto_backup(interval_minutes=5)  # Sauvegarde auto toutes les 5 min
        except Exception as e:
            self.log_to_console(f"⚠️ Erreur sauvegarde: {e}")
            
        if not os.path.exists(self.excel_file_path.get()):
            messagebox.showerror("Erreur", "Le fichier sélectionné n'existe pas")
            return
            
        if self.is_running:
            messagebox.showwarning("Attention", "Le traitement est déjà en cours")
            return
            
        # Vérifier que Clarify est ouvert
        if not self.check_clarify_window():
            response = messagebox.askyesno("Clarify non trouvé", 
                                         "Clarify ne semble pas être ouvert.\n"
                                         "Voulez-vous continuer quand même ?")
            if not response:
                return
        
        # Démarrer le traitement
        self.is_running = True
        self.stop_requested = False
        
        # Mettre à jour l'interface
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.browse_button.config(state='disabled')
        self.status_label.config(text="🔄 En cours...", foreground='green')
        
        # Démarrer le thread de traitement
        self.process_thread = threading.Thread(target=self.run_processing, daemon=True)
        self.process_thread.start()
        
        self.log_to_console("🚀 Démarrage du traitement...")
        
    def stop_processing(self):
        """Arrête le traitement"""
        if not self.is_running:
            return
            
        self.stop_requested = True
        self.log_to_console("🛑 Arrêt demandé... Veuillez patienter")

        # Arrêter la sauvegarde automatique
        try:
            self.backup_manager.stop_auto_backup()
        except Exception as e:
            self.log_to_console(f"⚠️ Erreur arrêt sauvegarde: {e}")

        # Mettre à jour l'interface
        self.stop_button.config(state='disabled')
        self.status_label.config(text="🛑 Arrêt en cours...", foreground='orange')
        
    def check_clarify_window(self):
        """Vérifie si Clarify est ouvert"""
        try:
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    if "Amdocs CRM - ClearSupport" in window_text or "CLARIFY" in window_text:
                        windows.append((hwnd, window_text))
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            return len(windows) > 0
        except:
            return False

    def run_processing(self):
        """Exécute le traitement principal dans un thread séparé"""
        try:
            # Importer les fonctions du script principal
            from clic_ID_2_simple import (
                find_clarify_window, activate_clarify, capture_and_click,
                detect_and_click_image, create_tracking_file, get_session_id,
                update_tracking_status, read_excel_ids, get_unprocessed_cases,
                process_single_case
            )

            # Générer ID de session unique
            session_id = get_session_id()
            self.log_to_console(f"🆔 Session ID: {session_id}")

            # Créer/charger le fichier de suivi
            tracking_file = create_tracking_file()
            self.log_to_console(f"📊 Fichier de suivi: {tracking_file}")

            # Lire les IDs depuis Excel
            excel_path = self.excel_file_path.get()
            all_case_ids = read_excel_ids(excel_path)

            if not all_case_ids:
                self.log_to_console("❌ Aucun ID trouvé dans le fichier Excel")
                self.processing_finished(False)
                return

            self.log_to_console(f"📋 {len(all_case_ids)} ID(s) trouvé(s) dans le fichier")

            # Déterminer les cas à traiter (exclure ceux déjà réussis)
            case_ids = get_unprocessed_cases(tracking_file, all_case_ids, session_id)

            if len(case_ids) == 0:
                self.log_to_console("🎉 TOUS LES CAS ONT DÉJÀ ÉTÉ TRAITÉS AVEC SUCCÈS!")
                self.processing_finished(True)
                return

            self.log_to_console(f"🔄 {len(case_ids)} cas à traiter dans cette session")

            # Vérifier les templates
            search_template_path = os.path.join("image", "search_icon_selected.png")
            suivi_template_path = os.path.join("image", "suivi_resolution.png")

            if not os.path.exists(search_template_path):
                self.log_to_console(f"❌ Template recherche manquant: {search_template_path}")
                self.processing_finished(False)
                return

            if not os.path.exists(suivi_template_path):
                self.log_to_console(f"❌ Template suivi résolution manquant: {suivi_template_path}")
                self.processing_finished(False)
                return

            # Trouver Clarify
            self.log_to_console("🔍 Recherche de Clarify...")
            windows = find_clarify_window()
            if not windows:
                self.log_to_console("❌ Clarify non trouvé")
                self.processing_finished(False)
                return

            hwnd = windows[0][0]
            self.log_to_console(f"✅ Clarify trouvé: {windows[0][1]}")

            # Activer Clarify
            self.log_to_console("🖥️ Activation de Clarify en plein écran...")
            activate_clarify(hwnd)

            if self.stop_requested:
                self.log_to_console("🛑 Arrêt demandé avant le traitement")
                self.processing_finished(False)
                return

            # Traitement en boucle
            self.log_to_console("🎯 DÉBUT DU TRAITEMENT EN MASSE")
            success_count = 0
            failed_count = 0

            for i, case_id in enumerate(case_ids, 1):
                if self.stop_requested:
                    self.log_to_console("🛑 Arrêt demandé pendant le traitement")
                    break

                self.log_to_console(f"{'='*30}")
                self.log_to_console(f"CAS {i}/{len(case_ids)}: {case_id}")
                self.log_to_console(f"{'='*30}")

                # Traiter le cas avec une version modifiée qui utilise notre système de log
                if self.process_single_case_gui(hwnd, case_id, search_template_path,
                                              suivi_template_path, tracking_file, session_id):
                    success_count += 1
                else:
                    failed_count += 1

                if self.stop_requested:
                    break

                # Pause entre les cas
                if i < len(case_ids) and not self.stop_requested:
                    self.log_to_console("⏸️ Pause avant le cas suivant...")
                    for j in range(20):  # 2 secondes
                        if self.stop_requested:
                            break
                        time.sleep(0.1)

            # Résumé final
            self.log_to_console("="*60)
            if self.stop_requested:
                self.log_to_console("🛑 TRAITEMENT INTERROMPU")
            else:
                self.log_to_console("🎉 TRAITEMENT TERMINÉ!")

            processed_count = i if 'i' in locals() else 0
            self.log_to_console(f"📊 RÉSUMÉ SESSION {session_id}:")
            self.log_to_console(f"   ✅ Réussis: {success_count}/{processed_count}")
            self.log_to_console(f"   ❌ Échecs: {failed_count}/{processed_count}")

            if self.stop_requested and processed_count < len(case_ids):
                remaining = len(case_ids) - processed_count
                self.log_to_console(f"   ⏭️ Non traités: {remaining}")

            self.log_to_console(f"📊 FICHIER DE SUIVI: {tracking_file}")

            self.processing_finished(True)

        except Exception as e:
            self.log_to_console(f"❌ ERREUR CRITIQUE: {e}")
            self.processing_finished(False)

    def process_single_case_gui(self, hwnd, case_id, search_template_path, suivi_template_path, tracking_file, session_id):
        """Version GUI du traitement d'un cas unique"""
        from clic_ID_2_simple import capture_and_click, detect_and_click_image, update_tracking_status

        start_time = datetime.now()

        self.log_to_console(f"🔄 Traitement du cas: {case_id}")

        # Enregistrer le début du traitement
        update_tracking_status(tracking_file, case_id, "EN_COURS", "", session_id, start_time)

        try:
            # Vérifier arrêt avant de commencer
            if self.stop_requested:
                update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur", session_id, start_time)
                return False

            # ÉTAPE 1: Clic sur icône de recherche
            self.log_to_console("  📍 Clic sur icône recherche...")
            if not capture_and_click(hwnd, search_template_path):
                error_msg = "Échec clic icône recherche"
                self.log_to_console(f"  ❌ {error_msg}")
                update_tracking_status(tracking_file, case_id, "ECHEC", error_msg, session_id, start_time)
                return False

            # Vérifier arrêt après clic
            if self.stop_requested:
                update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur après clic recherche", session_id, start_time)
                return False

            # ÉTAPE 2: Attendre ouverture fenêtre
            self.log_to_console("  ⏳ Attente ouverture fenêtre...")
            for i in range(30):  # 3 secondes en morceaux de 0.1s
                if self.stop_requested:
                    update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur pendant attente fenêtre", session_id, start_time)
                    return False
                time.sleep(0.1)

            # ÉTAPE 3: Saisir l'ID
            self.log_to_console(f"  ⌨️ Saisie: {case_id}")
            try:
                pyautogui.hotkey('ctrl', 'a')  # Sélectionner tout
                time.sleep(0.2)

                if self.stop_requested:
                    update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur avant saisie", session_id, start_time)
                    return False

                pyautogui.write(case_id)
                time.sleep(0.5)
            except Exception as e:
                error_msg = f"Erreur saisie ID: {e}"
                self.log_to_console(f"  ❌ {error_msg}")
                update_tracking_status(tracking_file, case_id, "ECHEC", error_msg, session_id, start_time)
                return False

            # ÉTAPE 4: Lancer recherche
            self.log_to_console("  🔍 Lancement recherche...")
            try:
                pyautogui.press('enter')
            except Exception as e:
                error_msg = f"Erreur lancement recherche: {e}"
                self.log_to_console(f"  ❌ {error_msg}")
                update_tracking_status(tracking_file, case_id, "ECHEC", error_msg, session_id, start_time)
                return False

            # Attendre résultats avec vérification d'arrêt
            self.log_to_console("  ⏳ Attente résultats...")
            for i in range(40):  # 4 secondes en morceaux de 0.1s
                if self.stop_requested:
                    update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur pendant attente résultats", session_id, start_time)
                    return False
                time.sleep(0.1)

            # NOUVELLES ÉTAPES AJOUTÉES
            # ÉTAPE 5: Clic sur Bureau
            bureau_template_path = os.path.join("image", "bureau.png")
            if os.path.exists(bureau_template_path):
                self.log_to_console("  🖥️ Clic sur Bureau...")
                if not capture_and_click(hwnd, bureau_template_path):
                    error_msg = "Bouton Bureau non trouvé"
                    self.log_to_console(f"  ⚠️ {error_msg} - Continuons quand même...")
                    # Ne pas retourner False, continuer le traitement
                else:
                    self.log_to_console("  ✅ Bureau cliqué avec succès")

                    # Vérifier arrêt après clic Bureau
                    if self.stop_requested:
                        update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur après clic Bureau", session_id, start_time)
                        return False

                    # Attendre un peu après le clic Bureau
                    time.sleep(1.0)
            else:
                self.log_to_console(f"  ⚠️ Template Bureau manquant: {bureau_template_path}")

            # ÉTAPE 6: Clic sur Reprendre
            reprendre_template_path = os.path.join("image", "reprendre.png")
            if os.path.exists(reprendre_template_path):
                self.log_to_console("  ▶️ Clic sur Reprendre...")
                if not capture_and_click(hwnd, reprendre_template_path):
                    error_msg = "Bouton Reprendre non trouvé"
                    self.log_to_console(f"  ⚠️ {error_msg} - Continuons quand même...")
                    # Ne pas retourner False, continuer le traitement
                else:
                    self.log_to_console("  ✅ Reprendre cliqué avec succès")

                    # Vérifier arrêt après clic Reprendre
                    if self.stop_requested:
                        update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur après clic Reprendre", session_id, start_time)
                        return False

                    # Attendre un peu après le clic Reprendre
                    time.sleep(1.0)
            else:
                self.log_to_console(f"  ⚠️ Template Reprendre manquant: {reprendre_template_path}")

            # ÉTAPE 7: Clic sur Accept Reprendre
            accept_reprendre_template_path = os.path.join("image", "accept_reprendre.png")
            if os.path.exists(accept_reprendre_template_path):
                self.log_to_console("  ✅ Clic sur Accept Reprendre...")
                if not capture_and_click(hwnd, accept_reprendre_template_path):
                    error_msg = "Bouton Accept Reprendre non trouvé"
                    self.log_to_console(f"  ⚠️ {error_msg} - Continuons quand même...")
                    # Ne pas retourner False, continuer le traitement
                else:
                    self.log_to_console("  ✅ Accept Reprendre cliqué avec succès")

                    # Vérifier arrêt après clic Accept Reprendre
                    if self.stop_requested:
                        update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur après clic Accept Reprendre", session_id, start_time)
                        return False

                    # Attendre un peu après le clic Accept Reprendre
                    time.sleep(1.5)
            else:
                self.log_to_console(f"  ⚠️ Template Accept Reprendre manquant: {accept_reprendre_template_path}")

            # Attendre un peu avant de continuer vers Suivi Résolution
            self.log_to_console("  ⏳ Attente avant Suivi Résolution...")
            for i in range(20):  # 2 secondes supplémentaires
                if self.stop_requested:
                    update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur avant Suivi Résolution", session_id, start_time)
                    return False
                time.sleep(0.1)

            # ÉTAPE 5: Cliquer sur suivi résolution
            self.log_to_console("  🎯 Recherche 'Suivi Résolution'...")
            if self.stop_requested:
                update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur avant suivi résolution", session_id, start_time)
                return False

            if detect_and_click_image(hwnd, suivi_template_path, "Suivi Résolution"):
                self.log_to_console(f"  ✅ Cas {case_id} traité avec succès!")
                update_tracking_status(tracking_file, case_id, "SUCCESS", "", session_id, start_time)

                # Délai avant le cas suivant avec vérification
                for i in range(20):  # 2 secondes en morceaux de 0.1s
                    if self.stop_requested:
                        return True  # Le cas actuel est réussi même si on s'arrête
                    time.sleep(0.1)

                return True
            else:
                error_msg = "Suivi Résolution non trouvé"
                self.log_to_console(f"  ⚠️ {error_msg}")
                update_tracking_status(tracking_file, case_id, "ECHEC", error_msg, session_id, start_time)
                return False

        except Exception as e:
            error_msg = f"Erreur inattendue: {e}"
            self.log_to_console(f"  ❌ {error_msg}")
            update_tracking_status(tracking_file, case_id, "ECHEC", error_msg, session_id, start_time)
            return False

    def processing_finished(self, success):
        """Appelée quand le traitement est terminé"""
        self.is_running = False

        # Mettre à jour l'interface
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.browse_button.config(state='normal')

        if success:
            self.status_label.config(text="✅ Terminé", foreground='green')
            self.log_to_console("✅ Traitement terminé avec succès!")
        else:
            self.status_label.config(text="❌ Erreur", foreground='red')
            self.log_to_console("❌ Traitement terminé avec des erreurs")

        # Afficher une notification
        if success:
            messagebox.showinfo("Traitement terminé",
                              "Le traitement s'est terminé avec succès!\n"
                              "Consultez la console pour les détails.")
        else:
            messagebox.showwarning("Traitement interrompu",
                                 "Le traitement s'est arrêté avec des erreurs.\n"
                                 "Consultez la console pour plus d'informations.")


def main():
    """Fonction principale pour lancer l'interface"""

    # Vérifier les dépendances
    try:
        import pandas as pd
        import win32gui
        import pyautogui
        import cv2
        from PIL import Image
    except ImportError as e:
        print(f"❌ Dépendance manquante: {e}")
        print("Installez les dépendances avec:")
        print("pip install pandas openpyxl pywin32 pyautogui opencv-python pillow")
        input("Appuyez sur Entrée pour quitter...")
        return

    # Créer et lancer l'interface
    root = tk.Tk()
    app = ClarifyGUI(root)

    # Gérer la fermeture de la fenêtre
    def on_closing():
        if app.is_running:
            if messagebox.askokcancel("Quitter", "Un traitement est en cours. Voulez-vous vraiment quitter ?"):
                app.stop_requested = True
                root.destroy()
        else:
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # Démarrer l'interface
    root.mainloop()


if __name__ == "__main__":
    main()
