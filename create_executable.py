#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour créer un exécutable de Clarify
Utilise PyInstaller pour générer un .exe autonome
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """Vérifie si PyInstaller est installé"""
    try:
        import PyInstaller
        print("✅ PyInstaller est installé")
        return True
    except ImportError:
        print("❌ PyInstaller n'est pas installé")
        return False

def install_pyinstaller():
    """Installe PyInstaller"""
    print("📦 Installation de PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installé avec succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de l'installation de PyInstaller: {e}")
        return False

def create_spec_file():
    """Crée le fichier .spec pour PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Données à inclure
added_files = [
    ('image', 'image'),
    ('clarify_config.json', '.'),
    ('clarify_config_simple.txt', '.'),
    ('requirements.txt', '.'),
    ('TT_liste.xlsx', '.'),
    ('*.md', '.'),
]

# Modules cachés nécessaires
hidden_imports = [
    'win32gui',
    'win32ui', 
    'win32con',
    'win32api',
    'pynput',
    'pynput.keyboard',
    'pynput.mouse',
    'pyautogui',
    'cv2',
    'numpy',
    'pandas',
    'openpyxl',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'threading',
    'queue',
    'datetime',
    'shutil',
    'backup_manager',
    'config_manager',
    'convert_config',
    'Selector_icone',
    'clic_ID_2_simple'
]

a = Analysis(
    ['lancer_interface.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Clarify_Automation',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='image/clarify_icon.ico' if os.path.exists('image/clarify_icon.ico') else None,
)
'''
    
    with open('clarify.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Fichier clarify.spec créé")

def build_executable():
    """Construit l'exécutable avec PyInstaller"""
    print("\n🔨 Construction de l'exécutable...")
    print("⏳ Cela peut prendre plusieurs minutes...")
    
    try:
        # Nettoyer les anciens builds
        if os.path.exists('build'):
            shutil.rmtree('build')
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        
        # Construire avec le fichier spec
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "clarify.spec"]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Exécutable créé avec succès!")
            return True
        else:
            print("❌ Erreur lors de la construction:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_portable_package():
    """Crée un package portable"""
    print("\n📦 Création du package portable...")
    
    # Créer le dossier de distribution
    dist_folder = "Clarify_Portable"
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    
    os.makedirs(dist_folder)
    
    # Copier l'exécutable
    exe_path = "dist/Clarify_Automation.exe"
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, dist_folder)
        print(f"✅ Exécutable copié dans {dist_folder}/")
    else:
        print("❌ Exécutable non trouvé")
        return False
    
    # Copier les fichiers essentiels
    files_to_copy = [
        "TT_liste.xlsx",
        "clarify_config.json", 
        "clarify_config_simple.txt",
        "README_Interface.md",
        "COMMENT_LANCER.md",
        "NETTOYAGE_ET_SAUVEGARDE.md"
    ]
    
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, dist_folder)
            print(f"✅ {file} copié")
    
    # Copier le dossier image
    if os.path.exists("image"):
        shutil.copytree("image", os.path.join(dist_folder, "image"))
        print("✅ Dossier image copié")
    
    # Créer un fichier de lancement simple
    launcher_content = '''@echo off
title Clarify Automation - Portable
echo.
echo CLARIFY AUTOMATION - VERSION PORTABLE
echo =====================================
echo.
echo Lancement de l'application...
echo.
Clarify_Automation.exe
echo.
echo L'application s'est fermee
pause
'''
    
    with open(os.path.join(dist_folder, "Lancer_Clarify.bat"), 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ Lanceur portable créé")
    
    # Créer un README pour la version portable
    readme_content = '''# CLARIFY AUTOMATION - VERSION PORTABLE

## 🚀 UTILISATION

1. **Lancement** : Double-cliquez sur `Lancer_Clarify.bat`
2. **Configuration** : Modifiez `TT_liste.xlsx` avec vos données
3. **Templates** : Les images sont dans le dossier `image/`

## 📁 CONTENU

- `Clarify_Automation.exe` - Application principale
- `Lancer_Clarify.bat` - Lanceur simple
- `TT_liste.xlsx` - Fichier de données
- `image/` - Templates d'images
- `clarify_config.json` - Configuration
- Documentation (fichiers .md)

## ⚙️ CONFIGURATION

Modifiez `clarify_config.json` ou `clarify_config_simple.txt` selon vos besoins.

## 🆘 SUPPORT

Consultez les fichiers .md pour la documentation complète.

---
Version portable générée automatiquement
'''
    
    with open(os.path.join(dist_folder, "README_PORTABLE.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"\n🎉 Package portable créé dans le dossier: {dist_folder}/")
    print(f"📁 Taille du package: {get_folder_size(dist_folder):.1f} MB")
    
    return True

def get_folder_size(folder_path):
    """Calcule la taille d'un dossier en MB"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            total_size += os.path.getsize(filepath)
    return total_size / (1024 * 1024)

def main():
    """Fonction principale"""
    print("🏗️  CRÉATION D'UN EXÉCUTABLE CLARIFY")
    print("=" * 50)
    
    # Vérifier PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("❌ Impossible d'installer PyInstaller")
            return
    
    # Créer le fichier spec
    create_spec_file()
    
    # Construire l'exécutable
    if not build_executable():
        print("❌ Échec de la construction")
        return
    
    # Créer le package portable
    if create_portable_package():
        print("\n🎉 SUCCÈS!")
        print("📦 Votre application portable est prête!")
        print("📁 Dossier: Clarify_Portable/")
        print("🚀 Lanceur: Clarify_Portable/Lancer_Clarify.bat")
        print("\n💡 Vous pouvez maintenant copier le dossier 'Clarify_Portable'")
        print("   sur n'importe quel PC Windows sans Python installé!")
    
    # Nettoyer les fichiers temporaires
    cleanup_files = ['clarify.spec', 'build', 'dist']
    for item in cleanup_files:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
            else:
                os.remove(item)
    
    print("\n🧹 Fichiers temporaires nettoyés")

if __name__ == "__main__":
    main()
