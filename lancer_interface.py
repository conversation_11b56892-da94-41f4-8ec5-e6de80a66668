#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface Clarify avec pynput - Nouvelle version
Automatisation des traitements Clarify
"""

import sys
import os
import time
import pandas as pd
import win32gui
import win32con
import win32ui
import ctypes
from ctypes import windll
from datetime import datetime
from PIL import Image
import cv2
import numpy as np
import random

# Import pynput
try:
    from pynput.keyboard import Key, Controller as KeyboardController
    PYNPUT_AVAILABLE = True
    print("✅ pynput disponible")
except ImportError:
    PYNPUT_AVAILABLE = False
    print("❌ pynput non disponible - Installation requise: pip install pynput")

# Variables globales
STOP_REQUESTED = False
keyboard = None

def check_dependencies():
    """Vérifie que toutes les dépendances sont installées"""

    missing_deps = []

    # Liste des dépendances requises (mise à jour avec pynput et détection d'image)
    dependencies = [
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('win32gui', 'pywin32'),
        ('pynput', 'pynput'),
        ('cv2', 'opencv-python'),
        ('PIL', 'pillow'),
        ('tkinter', 'tkinter (inclus avec Python)')
    ]

    print("🔍 Vérification des dépendances...")

    for module_name, package_name in dependencies:
        try:
            __import__(module_name)
            print(f"  ✅ {package_name}")
        except ImportError:
            print(f"  ❌ {package_name}")
            missing_deps.append(package_name)

    if missing_deps:
        print(f"\n❌ Dépendances manquantes: {', '.join(missing_deps)}")
        print("\n📦 Pour installer les dépendances manquantes, exécutez:")

        # Filtrer tkinter car il est inclus avec Python
        installable_deps = [dep for dep in missing_deps if 'tkinter' not in dep.lower()]

        if installable_deps:
            print(f"pip install {' '.join(installable_deps)}")

        if any('tkinter' in dep.lower() for dep in missing_deps):
            print("\n⚠️  tkinter n'est pas disponible.")
            print("   Sur Ubuntu/Debian: sudo apt-get install python3-tk")
            print("   Sur CentOS/RHEL: sudo yum install tkinter")
            print("   Sur Windows: tkinter devrait être inclus avec Python")

        return False

    print("\n✅ Toutes les dépendances sont installées!")
    return True

def find_clarify_window():
    """Trouve la fenêtre Clarify"""
    def enum_windows_callback(hwnd, windows):
        if win32gui.IsWindowVisible(hwnd):
            window_text = win32gui.GetWindowText(hwnd)
            if "Amdocs CRM - ClearSupport" in window_text or "CLARIFY" in window_text:
                windows.append((hwnd, window_text))
        return True

    windows = []
    win32gui.EnumWindows(enum_windows_callback, windows)
    return windows

def activate_clarify_window(hwnd):
    """Active la fenêtre Clarify avec focus forcé"""
    try:
        print("🔄 Activation de Clarify...")

        # Méthode 1: Activation standard
        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
        win32gui.SetForegroundWindow(hwnd)
        win32gui.BringWindowToTop(hwnd)
        time.sleep(0.5)

        # Méthode 2: Forçage avec attachement de threads
        try:
            current_thread = ctypes.windll.kernel32.GetCurrentThreadId()
            target_thread = ctypes.windll.user32.GetWindowThreadProcessId(hwnd, None)

            if target_thread != current_thread:
                ctypes.windll.user32.AttachThreadInput(current_thread, target_thread, True)
                win32gui.SetForegroundWindow(hwnd)
                win32gui.BringWindowToTop(hwnd)
                ctypes.windll.user32.AttachThreadInput(current_thread, target_thread, False)
            else:
                win32gui.SetForegroundWindow(hwnd)
                win32gui.BringWindowToTop(hwnd)

            time.sleep(0.5)
        except Exception as e:
            print(f"⚠️  Forçage threads échoué: {e}")

        # Vérifier le focus plusieurs fois
        for attempt in range(3):
            foreground_hwnd = win32gui.GetForegroundWindow()
            if foreground_hwnd == hwnd:
                print("✅ Clarify activé et au premier plan!")
                return True

            print(f"🔄 Tentative {attempt + 1}/3 de focus...")
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.3)

        print("⚠️  Clarify activé mais focus incertain")
        return True  # On continue quand même

    except Exception as e:
        print(f"❌ Erreur activation Clarify: {e}")
        return False

def ensure_clarify_focus(hwnd):
    """S'assure que Clarify a le focus avant chaque action"""
    try:
        win32gui.SetForegroundWindow(hwnd)
        time.sleep(0.2)
        return True
    except:
        return False

def capture_window(hwnd):
    """Capture la fenêtre Clarify"""
    try:
        hwndDC = win32gui.GetWindowDC(hwnd)
        mfcDC = win32ui.CreateDCFromHandle(hwndDC)
        saveDC = mfcDC.CreateCompatibleDC()

        rect = win32gui.GetWindowRect(hwnd)
        width = rect[2] - rect[0]
        height = rect[3] - rect[1]

        saveBitMap = win32ui.CreateBitmap()
        saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
        saveDC.SelectObject(saveBitMap)

        result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 3)

        if result:
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                                 bmpstr, 'raw', 'BGRX', 0, 1)

            # Nettoyage
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, hwndDC)

            return img

        return None

    except Exception as e:
        print(f"❌ Erreur capture: {e}")
        return None

def detect_and_click_image(hwnd, template_path, description="image"):
    """Détecte et clique sur une image template dans Clarify"""

    if not os.path.exists(template_path):
        print(f"❌ Template {description} non trouvé: {template_path}")
        return False

    try:
        print(f"🎯 Recherche de {description}...")

        # Capture de la fenêtre
        screenshot = capture_window(hwnd)
        if screenshot is None:
            print(f"❌ Impossible de capturer la fenêtre")
            return False

        # Conversion pour détection
        screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        template = cv2.imread(template_path)

        if template is not None:
            # Détection
            screenshot_gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            print(f"📊 Score détection {description}: {max_val:.3f}")

            # Seuils pour différents types d'images
            thresholds = [0.7, 0.6, 0.5, 0.4]

            for threshold in thresholds:
                if max_val >= threshold:
                    template_h, template_w = template_gray.shape
                    center_x = max_loc[0] + template_w // 2
                    center_y = max_loc[1] + template_h // 2

                    print(f"✅ {description} détecté! (seuil: {threshold})")
                    print(f"   Position: ({center_x}, {center_y})")

                    # Clic avec pynput (plus fiable)
                    rect = win32gui.GetWindowRect(hwnd)
                    abs_x = rect[0] + center_x
                    abs_y = rect[1] + center_y

                    print(f"👆 Clic sur {description} à ({abs_x}, {abs_y})...")

                    # Utiliser pynput pour le clic
                    try:
                        from pynput.mouse import Button
                        from pynput import mouse

                        mouse_controller = mouse.Controller()
                        mouse_controller.position = (abs_x, abs_y)
                        time.sleep(0.1)
                        mouse_controller.click(Button.left, 1)
                        time.sleep(0.5)

                        print(f"✅ Clic effectué sur {description}")
                        return True

                    except Exception as e:
                        print(f"⚠️  Erreur clic pynput: {e}")
                        # Fallback avec pyautogui si disponible
                        try:
                            import pyautogui
                            pyautogui.click(abs_x, abs_y)
                            time.sleep(0.5)
                            print(f"✅ Clic effectué sur {description} (fallback)")
                            return True
                        except:
                            print(f"❌ Impossible de cliquer sur {description}")
                            return False

            print(f"❌ {description} non détecté (score max: {max_val:.3f})")
            return False
        else:
            print(f"❌ Impossible de charger le template {description}")
            return False

    except Exception as e:
        print(f"❌ Erreur détection {description}: {e}")
        return False

def read_excel_data(excel_path="TT_liste.xlsx"):
    """Lit les IDs (colonne A), codes B, C et D depuis le fichier Excel"""

    if not os.path.exists(excel_path):
        print(f"❌ Fichier Excel non trouvé: {excel_path}")
        return []

    try:
        print(f"📖 Lecture du fichier: {excel_path}")

        # Lire le fichier Excel colonnes A, B, C, D et E
        df = pd.read_excel(excel_path, usecols=[0, 1, 2, 3, 4], header=None)  # Colonnes A (0), B (1), C (2), D (3), E (4)

        # Extraire les données
        cases_data = []
        for _, row in df.iterrows():
            case_id = str(row[0]).strip() if pd.notna(row[0]) else ""
            code_b = str(row[1]).strip() if pd.notna(row[1]) else ""
            code_c = str(row[2]).strip() if pd.notna(row[2]) else ""
            code_d = str(row[3]).strip() if pd.notna(row[3]) else ""
            code_e = str(row[4]).strip() if pd.notna(row[4]) else ""

            # Filtrer les lignes vides
            if case_id and case_id.lower() != 'nan':
                cases_data.append({
                    'id': case_id,
                    'code_b': code_b,
                    'code_c': code_c,
                    'code_d': code_d,
                    'code_e': code_e
                })

        print(f"✅ {len(cases_data)} cas trouvé(s):")
        for i, case_data in enumerate(cases_data[:5], 1):  # Afficher seulement les 5 premiers
            info_parts = []
            if case_data['code_b']:
                info_parts.append(f"B: {case_data['code_b']}")
            if case_data['code_c']:
                info_parts.append(f"C: {case_data['code_c']}")
            if case_data['code_d']:
                info_parts.append(f"D: {case_data['code_d']}")
            if case_data['code_e']:
                info_parts.append(f"E: {case_data['code_e']}")

            info_str = f" ({', '.join(info_parts)})" if info_parts else ""
            print(f"   {i}. {case_data['id']}{info_str}")
        if len(cases_data) > 5:
            print(f"   ... et {len(cases_data) - 5} autres")

        return cases_data

    except Exception as e:
        print(f"❌ Erreur lecture Excel: {e}")
        return []

def read_excel_ids(excel_path="TT_liste.xlsx"):
    """Fonction de compatibilité - retourne seulement les IDs"""
    cases_data = read_excel_data(excel_path)
    return [case['id'] for case in cases_data]

def check_files():
    """Vérifie que les fichiers nécessaires sont présents"""

    print("\n🔍 Vérification des fichiers...")

    # Vérifier le fichier Excel principal
    if os.path.exists('TT_liste.xlsx'):
        print(f"  ✅ TT_liste.xlsx")
    else:
        print(f"  ❌ TT_liste.xlsx (fichier requis)")
        return False

    print("\n✅ Fichier Excel trouvé!")
    return True

def send_ctrl_i():
    """Envoie Ctrl+I avec pynput"""
    try:
        print("⌨️  Envoi de Ctrl+I...")
        with keyboard.pressed(Key.ctrl):
            keyboard.press('i')
            keyboard.release('i')
        time.sleep(1.0)  # 0.5s → 1.0s (doublé)
        print("   ✅ Ctrl+I envoyé")
        return True
    except Exception as e:
        print(f"   ❌ Erreur Ctrl+I: {e}")
        return False

def send_text(text):
    """Envoie du texte avec pynput"""
    try:
        print(f"⌨️  Saisie: {text}")
        keyboard.type(text)
        time.sleep(0.3)
        print(f"   ✅ Texte '{text}' saisi")
        return True
    except Exception as e:
        print(f"   ❌ Erreur saisie: {e}")
        return False

def send_enter():
    """Envoie la touche Entrée"""
    try:
        print("⌨️  Appui sur Entrée...")
        keyboard.press(Key.enter)
        keyboard.release(Key.enter)
        time.sleep(1.0)  # 0.5s → 1.0s (doublé)
        print("   ✅ Entrée envoyée")
        return True
    except Exception as e:
        print(f"   ❌ Erreur Entrée: {e}")
        return False

def send_ctrl_shift_y():
    """Envoie Ctrl+Shift+Y avec pynput - méthode qui fonctionne"""
    try:
        print("⌨️  Envoi de Ctrl+Shift+Y...")
        with keyboard.pressed(Key.ctrl):
            with keyboard.pressed(Key.shift):
                keyboard.press('y')
                keyboard.release('y')
        time.sleep(1.0)
        print("   ✅ Ctrl+Shift+Y envoyé")
        return True
    except Exception as e:
        print(f"   ❌ Erreur Ctrl+Shift+Y: {e}")
        return False

def send_key(key_char):
    """Envoie une touche simple avec pynput"""
    try:
        keyboard.press(key_char)
        keyboard.release(key_char)
        time.sleep(0.3)
        return True
    except Exception as e:
        print(f"   ❌ Erreur envoi touche {key_char}: {e}")
        return False

def detect_text_in_window(hwnd, target_text):
    """Détecte si un texte est présent dans la fenêtre Clarify"""
    try:
        # Capture de la fenêtre
        screenshot = capture_window(hwnd)
        if screenshot is None:
            return False

        # Conversion pour OCR (optionnel - pour l'instant on simule)
        # Pour une vraie détection de texte, il faudrait pytesseract
        # Ici on simule en cherchant des patterns visuels

        # Pour l'instant, on retourne True après quelques tentatives
        # Dans une vraie implémentation, on utiliserait OCR
        return False  # Sera remplacé par une vraie détection

    except Exception as e:
        print(f"❌ Erreur détection texte: {e}")
        return False

def open_dropdown_and_select(target_option, navigation_keys):
    """Ouvre un menu déroulant avec V et navigue avec les touches spécifiées"""
    try:
        print(f"🔽 Ouverture menu déroulant pour '{target_option}'...")

        # Appuyer sur V pour ouvrir le menu déroulant
        if not send_key('v'):
            print("   ❌ Échec ouverture menu (touche V)")
            return False

        time.sleep(0.2)  # Pause pour laisser le menu s'ouvrir (0.4s / 2)

        # Naviguer avec les touches spécifiées
        print(f"   🧭 Navigation: {navigation_keys}")
        for key in navigation_keys:
            if not send_key(key):
                print(f"   ❌ Échec navigation (touche {key})")
                return False
            time.sleep(0.125)  # Pause entre chaque navigation (0.25s / 2)

        # Appuyer sur Entrée pour valider la sélection
        print("   ⏎ Validation avec Entrée...")
        if not send_enter():
            print("   ❌ Échec validation (Entrée)")
            return False

        print(f"   ✅ '{target_option}' sélectionné avec succès!")
        return True

    except Exception as e:
        print(f"❌ Erreur open_dropdown_and_select: {e}")
        return False

def send_tab(count=1):
    """Envoie la touche Tab un certain nombre de fois"""
    try:
        print(f"⌨️  Envoi de {count} Tab...")
        for _ in range(count):
            keyboard.press(Key.tab)
            keyboard.release(Key.tab)
            time.sleep(0.2)
        print(f"   ✅ {count} Tab envoyé(s)")
        return True
    except Exception as e:
        print(f"   ❌ Erreur envoi Tab: {e}")
        return False

def get_column_b_navigation(code_b):
    """Détermine les touches de navigation pour le code B"""
    if not code_b:
        return []

    code_upper = code_b.upper()

    # Mapping des codes B vers les touches de navigation
    if code_upper == "TIERS":
        return ['t']  # V puis T pour TIERS
    elif code_upper == "OI":
        return ['o']  # V puis O une fois pour OI
    elif code_upper == "OC":
        return ['o', 'o']  # V puis O deux fois pour OC
    else:
        # Pour d'autres codes, essayer la première lettre
        return [code_upper[0].lower()] if code_upper else []

def get_column_c_navigation(code_c):
    """Détermine les touches de navigation pour le code C"""
    if not code_c:
        return []

    code_upper = code_c.upper()

    # Mapping des codes C vers les touches de navigation
    # Ordre dans le menu: PBO (1x P), PM (2x P), PM-PBO (3x P), PTO (4x P), RACCO PALIER (R), HORIZONTALE RESEAU (H)
    mappings = {
        "PBO": ['p'],  # V puis P une fois pour PBO (1ère position P)
        "PM": ['p', 'p'],  # V puis P deux fois pour PM (2ème position P)
        "PM-PBO": ['p', 'p', 'p'],  # V puis P trois fois pour PM-PBO (3ème position P)
        "PTO": ['p', 'p', 'p', 'p'],  # V puis P quatre fois pour PTO (4ème position P)
        "RACCO PALIER": ['r'],  # V puis R pour RACCO PALIER
        "HORIZONTALE RESEAU": ['h'],  # V puis H pour HORIZONTALE RESEAU
        # Anciens mappings pour compatibilité
        "PRDM": ['p', 'r'],  # V puis P puis R pour PRDM
        "PM-PRDM": ['p', 'm'],  # V puis P puis M pour PM-PRDM
        "BRAM": ['b']  # V puis B pour BRAM
    }

    return mappings.get(code_upper, [code_upper[0].lower()] if code_upper else [])

def process_column_b(code_b):
    """Traite la logique de la colonne B avec menu déroulant"""
    if not code_b:
        print("📝 Colonne B vide → Pas d'action")
        return True

    print(f"🔍 Traitement colonne B: {code_b}")
    navigation_keys = get_column_b_navigation(code_b)

    if not navigation_keys:
        print(f"⚠️  Aucune correspondance trouvée pour '{code_b}'")
        return True

    print(f"🔽 V puis navigation: {navigation_keys}")

    # Utiliser la nouvelle méthode avec menu déroulant
    if open_dropdown_and_select(code_b, navigation_keys):
        print(f"✅ '{code_b}' sélectionné avec succès!")
        return True
    else:
        print(f"⚠️  Sélection '{code_b}' échouée")
        return False

def process_column_c(code_c):
    """Traite la logique de la colonne C avec menu déroulant"""
    if not code_c:
        print("📝 Colonne C vide → Pas d'action")
        return True

    print(f"🔍 Traitement colonne C: {code_c}")
    navigation_keys = get_column_c_navigation(code_c)

    if not navigation_keys:
        print(f"⚠️  Aucune correspondance trouvée pour '{code_c}'")
        return True

    print(f"🔽 V puis navigation: {navigation_keys}")

    # Utiliser la nouvelle méthode avec menu déroulant
    if open_dropdown_and_select(code_c, navigation_keys):
        print(f"✅ '{code_c}' sélectionné avec succès!")
        return True
    else:
        print(f"⚠️  Sélection '{code_c}' échouée")
        return False

def get_column_d_repeat_count(code_d):
    """Détermine le nombre de répétitions de la première lettre basé sur le chiffre dans code_d"""
    if not code_d or len(code_d) < 4:
        return 1  # Par défaut 1 répétition

    try:
        # Extraire seulement les chiffres après les 3 premières lettres
        # Format: RET02#PRELOC ERRONEE → on veut juste "02"
        # Prendre les caractères 3 et 4 (positions 3-4) qui sont les chiffres
        if len(code_d) >= 5:
            number_part = code_d[3:5]  # Prendre exactement 2 caractères (les chiffres)
        else:
            number_part = code_d[3:]  # Prendre ce qui reste si moins de 5 caractères

        # Convertir en entier (enlève les zéros de tête automatiquement)
        repeat_count = int(number_part)
        return max(1, repeat_count)  # Au minimum 1 répétition
    except (ValueError, IndexError):
        return 1  # Par défaut si erreur de parsing

def process_column_d_final(code_d):
    """Traite la logique finale de la colonne D basée sur le chiffre"""
    if not code_d:
        print("📝 Colonne D vide → Pas d'action finale")
        return True

    print(f"🔍 Traitement final colonne D: '{code_d}'")

    # Extraire la première lettre et le nombre de répétitions
    first_letter = code_d[0].lower() if code_d else 'r'
    repeat_count = get_column_d_repeat_count(code_d)

    print(f"📝 Code D analysé: '{code_d}'")
    print(f"📝 Première lettre extraite: '{first_letter.upper()}'")
    print(f"📝 Nombre de répétitions calculé: {repeat_count}")

    # Créer la liste de navigation (répéter la première lettre)
    navigation_keys = [first_letter] * repeat_count

    print(f"🔽 V puis navigation: {navigation_keys}")

    # Utiliser la méthode avec menu déroulant
    if open_dropdown_and_select(f"Code {first_letter.upper()} x{repeat_count}", navigation_keys):
        print(f"✅ Code D final sélectionné avec succès!")
        return True
    else:
        print(f"⚠️  Sélection code D final échouée")
        return False

def test_ctrl_shift_y():
    """Fonction de test pour Ctrl+Shift+Y sur le ticket C34131148"""
    print("\n🧪 TEST CTRL+SHIFT+Y")
    print("=" * 50)
    print("Test spécifique: Ouverture C34131148 + Ctrl+Shift+Y")
    print("Assurez-vous que Clarify est ouvert et visible.")
    print("🚀 Démarrage du test...")

    # Initialiser le contrôleur clavier
    global keyboard
    keyboard = KeyboardController()

    try:
        # Rechercher Clarify
        windows = find_clarify_window()
        if not windows:
            print("❌ Clarify non trouvé!")
            return

        hwnd = windows[0][0]
        print(f"✅ Clarify trouvé")

        # Activer Clarify
        if not activate_clarify_window(hwnd):
            print("❌ Impossible d'activer Clarify")
            return

        print("✅ Clarify activé")
        time.sleep(2)

        # ÉTAPE 1: Ctrl+I
        print("\n🔍 ÉTAPE 1: Ctrl+I")
        if not send_ctrl_i():
            print("❌ Échec Ctrl+I")
            return
        print("✅ Ctrl+I réussi")

        # ÉTAPE 2: Saisir C34131148
        print("\n⌨️  ÉTAPE 2: Saisie C34131148")
        if not send_text("C34131148"):
            print("❌ Échec saisie")
            return
        print("✅ Saisie réussie")

        # ÉTAPE 3: Entrée
        print("\n⏎ ÉTAPE 3: Entrée (recherche)")
        print("⌨️  Entrée (recherche)...")
        keyboard.press(Key.enter)
        keyboard.release(Key.enter)
        time.sleep(1.0)
        print("✅ Entrée envoyée")

        # ÉTAPE 4: Attente 6 secondes
        print("\n⏳ ÉTAPE 4: Attente 6 secondes")
        for i in range(6, 0, -1):
            print(f"   ⏳ {i} secondes restantes...")
            time.sleep(1.0)
        print("✅ Attente terminée")

        # ÉTAPE 5: Test Ctrl+Shift+Y
        print("\n🔒 ÉTAPE 5: Test Ctrl+Shift+Y")
        print("🎯 Tentative 1/3...")
        if send_ctrl_shift_y():
            print("✅ Ctrl+Shift+Y réussi!")
        else:
            print("❌ Ctrl+Shift+Y échoué, tentative 2/3...")
            time.sleep(1)
            if send_ctrl_shift_y():
                print("✅ Ctrl+Shift+Y réussi (2ème tentative)!")
            else:
                print("❌ Ctrl+Shift+Y échoué, tentative 3/3...")
                time.sleep(1)
                if send_ctrl_shift_y():
                    print("✅ Ctrl+Shift+Y réussi (3ème tentative)!")
                else:
                    print("❌ Ctrl+Shift+Y échoué définitivement")

        print("\n🎉 TEST TERMINÉ!")
        print("Vérifiez manuellement si le cas a été pris en force dans Clarify.")

    except Exception as e:
        print(f"❌ Erreur test: {e}")

def check_boxes_manual():
    """Fonction pour cocher manuellement les cases depuis le mode console"""
    print("\n🔲 COCHAGE DES CASES")
    print("=" * 30)
    print("Cette fonction va essayer de cocher les cases 'Début de traitement corrigé' et 'Fin de traitement corrigé'")
    print("Assurez-vous que Clarify est ouvert et visible.")
    print("🚀 Démarrage automatique...")

    try:
        # Rechercher Clarify
        windows = find_clarify_window()
        if not windows:
            print("❌ Clarify non trouvé!")
            return

        hwnd = windows[0][0]
        ensure_clarify_focus(hwnd)

        print("🔲 Tentative de cochage...")

        # Méthode 1: Raccourcis Alt
        print("   🔲 Essai Alt+D...")
        try:
            keyboard.press(Key.alt)
            keyboard.press('d')
            keyboard.release('d')
            keyboard.release(Key.alt)
            time.sleep(0.5)
        except:
            pass

        print("   🔲 Essai Alt+F...")
        try:
            keyboard.press(Key.alt)
            keyboard.press('f')
            keyboard.release('f')
            keyboard.release(Key.alt)
            time.sleep(0.5)
        except:
            pass

        # Méthode 2: Navigation Tab
        print("   🔲 Navigation avec Tab...")
        for _ in range(10):
            keyboard.press(Key.tab)
            keyboard.release(Key.tab)
            time.sleep(0.2)

            keyboard.press(Key.space)
            keyboard.release(Key.space)
            time.sleep(0.2)

        print("✅ Tentative terminée")

    except Exception as e:
        print(f"❌ Erreur: {e}")

def process_single_case(case_data, case_number, total_cases, clarify_hwnd):
    """Traite un seul cas avec les nouvelles étapes et ciblage Clarify"""

    case_id = case_data['id']
    code_b = case_data['code_b']
    code_c = case_data['code_c']
    code_d = case_data['code_d']
    code_e = case_data['code_e']

    print(f"\n� Traitement du cas {case_number}/{total_cases}: {case_id}")
    if code_b:
        print(f"📋 Code B: {code_b}")
    if code_c:
        print(f"📋 Code C: {code_c}")
    if code_d:
        print(f"📋 Code D: {code_d}")
    if code_e:
        print(f"📋 Code E: {code_e}")
    print("-" * 50)

    try:
        # S'assurer que Clarify a le focus avant de commencer
        ensure_clarify_focus(clarify_hwnd)

        # ÉTAPE 1: Ctrl+I
        if not send_ctrl_i():
            print("❌ Échec Ctrl+I")
            return False

        # Petite pause
        time.sleep(1)

        # ÉTAPE 2: Saisir l'ID
        if not send_text(case_id):
            print("❌ Échec saisie ID")
            return False

        # Petite pause
        time.sleep(1.0)  # 0.5s → 1.0s (doublé)

        # ÉTAPE 3: Appuyer sur Entrée (recherche)
        print("⌨️  Entrée (recherche)...")
        keyboard.press(Key.enter)
        keyboard.release(Key.enter)
        time.sleep(1.0)

        # Attendre 6 secondes pour laisser le cas s'ouvrir complètement
        print("⏳ Attente 6 secondes pour ouverture du cas...")
        time.sleep(6.0)

        # ÉTAPE 4: Ctrl+Shift+Y (prendre le cas en force) - avec forçage de focus
        print("🔒 Prise en force du cas...")
        success = False

        for attempt in range(1, 4):
            print(f"🎯 Tentative {attempt}/3 de prise en force...")

            # Forcer le focus avant chaque tentative (comme dans le test qui marche)
            try:
                win32gui.SetForegroundWindow(clarify_hwnd)
                time.sleep(0.5)
                print("   🎯 Focus Clarify forcé")
            except Exception as e:
                print(f"   ⚠️  Erreur focus: {e}")

            # Utiliser la méthode qui fonctionne
            if send_ctrl_shift_y():
                print(f"✅ Ctrl+Shift+Y réussi (tentative {attempt})!")
                success = True
                break
            else:
                print(f"❌ Ctrl+Shift+Y échoué (tentative {attempt})")
                if attempt < 3:
                    time.sleep(1)

        if not success:
            print("❌ Échec définitif Ctrl+Shift+Y après 3 tentatives")
            return False

        # ÉTAPE 5: Entrée (après Ctrl+Shift+Y)
        print("⌨️  Entrée (confirmation)...")
        if not send_enter():
            print("❌ Échec Entrée confirmation")
            return False

        # ÉTAPE 6: Attente 1 seconde
        print("⏳ Attente 1 seconde...")
        time.sleep(1.0)

        # ÉTAPE 7: Entrée finale
        print("⌨️  Entrée (finale)...")
        if not send_enter():
            print("❌ Échec Entrée finale")
            return False

        # ÉTAPE 7.5: Attente supplémentaire de 8 secondes après Entrée finale
        print("⏳ Attente supplémentaire de 8 secondes après Entrée finale...")
        time.sleep(8.0)

        # ÉTAPE 8: Attente 8-10 secondes
        wait_time = random.randint(8, 10)
        print(f"⏳ Attente {wait_time} secondes avant clic sur Suivi Résolution...")
        time.sleep(wait_time)

        # ÉTAPE 9: Navigation avec Ctrl+Tab au lieu de clic sur image
        print("🎯 Navigation Ctrl+Tab vers Suivi Résolution...")

        # Maintenir Ctrl et faire 7x Tab
        print("⌨️  Ctrl maintenu + 7x Tab...")
        try:
            keyboard.press(Key.ctrl)
            for _ in range(7):
                keyboard.press(Key.tab)
                keyboard.release(Key.tab)
                time.sleep(0.2)  # Petite pause entre chaque Tab
            keyboard.release(Key.ctrl)
            print("✅ Ctrl+7xTab terminé!")

            # Attendre 8 secondes
            print("⏳ Attente 8 secondes...")
            time.sleep(8)
            print("✅ Navigation vers Suivi Résolution terminée!")

        except Exception as e:
            print(f"❌ Erreur navigation Ctrl+Tab: {e}")
            return False

        # ÉTAPE 10: Logique conditionnelle basée sur la colonne D
        if code_d:
                print(f"🔍 Analyse du code D: {code_d}")

                # Extraire le préfixe du code (ERR, RET, STT, ABS, EXC)
                code_upper = code_d.upper()

                if code_upper.startswith("RET"):
                    print("📝 Code RET → V puis R R pour 'Réussi'")
                    if open_dropdown_and_select("Réussi", ['r', 'r']):
                        print("✅ 'Réussi' sélectionné avec succès!")
                    else:
                        print("⚠️  Sélection 'Réussi' échouée")

                elif code_upper.startswith("ERR"):
                    print("📝 Code ERR → V puis E pour 'Erreur'")
                    if open_dropdown_and_select("Erreur", ['e']):
                        print("✅ 'Erreur' sélectionné avec succès!")
                    else:
                        print("⚠️  Sélection 'Erreur' échouée")

                elif code_upper.startswith("STT"):
                    print("📝 Code STT → V puis T pour 'Transmis à tort'")
                    if open_dropdown_and_select("Transmis à tort", ['t']):
                        print("✅ 'Transmis à tort' sélectionné avec succès!")
                        # Délai supplémentaire pour STT car position différente dans le menu
                        time.sleep(0.5)
                    else:
                        print("⚠️  Sélection 'Transmis à tort' échouée")

                elif code_upper.startswith("ABS"):
                    print("📝 Code ABS → V puis T pour 'Transmis à tort'")
                    if open_dropdown_and_select("Transmis à tort", ['t']):
                        print("✅ 'Transmis à tort' sélectionné avec succès!")
                    else:
                        print("⚠️  Sélection 'Transmis à tort' échouée")

                elif code_upper.startswith("EXC"):
                    print("📝 Code EXC → V puis T pour 'Transmis à tort'")
                    if open_dropdown_and_select("Transmis à tort", ['t']):
                        print("✅ 'Transmis à tort' sélectionné avec succès!")
                    else:
                        print("⚠️  Sélection 'Transmis à tort' échouée")

                else:
                    print(f"📝 Code non reconnu '{code_d}' → V puis première lettre")
                    first_letter = code_upper[0].lower() if code_upper else 't'
                    if open_dropdown_and_select(f"Code {first_letter.upper()}", [first_letter]):
                        print(f"✅ 'Code {first_letter.upper()}' sélectionné avec succès!")
                    else:
                        print(f"⚠️  Sélection 'Code {first_letter.upper()}' échouée")
        else:
            print("📝 Aucun code D → Pas d'action supplémentaire")

        # NOUVELLE ÉTAPE: Tab après sélection D (nombre variable selon le code)
        if code_d and code_d.upper().startswith("STT"):
            # Pour STT, peut-être besoin d'un nombre différent de Tab
            print("⌨️  2x Tab après sélection STT (position différente)...")
            if not send_tab(2):
                print("❌ Échec envoi 2x Tab")
                return False
        else:
            # Pour les autres codes (ERR, RET, etc.)
            print("⌨️  1x Tab après sélection D...")
            if not send_tab(1):
                print("❌ Échec envoi 1x Tab")
                return False

        # ÉTAPE 6: 1x Tab → Shift+PageDown → Suppr → Colonne E → 1x Tab → Barre espace → Attendre 0.5s → 4x Tab
        if code_e:
            print(f"🎯 Étape 6: 1x Tab → Shift+PageDown → Suppr → Colonne E → 1x Tab → Barre espace → Attendre 0.5s → 4x Tab")
            try:
                # Shift+PageDown
                keyboard.press(Key.shift)
                keyboard.press(Key.page_down)
                keyboard.release(Key.page_down)
                keyboard.release(Key.shift)
                time.sleep(0.25)

                # Suppr
                keyboard.press(Key.delete)
                keyboard.release(Key.delete)
                time.sleep(0.25)

                # Saisir le texte de la colonne E
                if not send_text(code_e):
                    print("❌ Échec saisie colonne E")
                    return False
                print(f"✅ Colonne E saisie: '{code_e}'")
                time.sleep(0.25)

                # 1x Tab
                print("⌨️  1x Tab...")
                if not send_tab(1):
                    print("❌ Échec envoi 1x Tab")
                    return False
                time.sleep(0.25)

                # Barre espace
                print("⌨️  Barre espace...")
                keyboard.press(Key.space)
                keyboard.release(Key.space)

                # Attendre 0.5s
                print("⏳ Attente 0.5 seconde...")
                time.sleep(0.5)

                # 4x Tab
                print("⌨️  4x Tab...")
                if not send_tab(4):
                    print("❌ Échec envoi 4x Tab")
                    return False

            except Exception as e:
                print(f"❌ Erreur Étape 6: {e}")
                return False
        else:
            print("📝 Colonne E vide → Étape 6 simplifiée: 1x Tab → Barre espace → Attendre 0.5s → 4x Tab")
            try:
                # 1x Tab
                if not send_tab(1):
                    print("❌ Échec envoi 1x Tab")
                    return False
                time.sleep(0.25)

                # Barre espace
                keyboard.press(Key.space)
                keyboard.release(Key.space)

                # Attendre 0.5s
                print("⏳ Attente 0.5 seconde...")
                time.sleep(0.5)

                # 4x Tab
                if not send_tab(4):
                    print("❌ Échec envoi 4x Tab")
                    return False

            except Exception as e:
                print(f"❌ Erreur Étape 6 simplifiée: {e}")
                return False

        # Attendre 0.25 seconde après les 4 tabs (0.5s / 2)
        print("⏳ Attente 0.25 seconde...")
        time.sleep(0.25)

        # ÉTAPE 12: Traitement colonne B
        print(f"🔍 DÉBUT traitement colonne B avec valeur: '{code_b}'")
        if not process_column_b(code_b):
            print("⚠️  Traitement colonne B échoué, on continue...")
        else:
            print("✅ Traitement colonne B terminé avec succès")

        # Attendre 0.25 seconde après colonne B (0.5s / 2)
        print("⏳ Attente 0.25 seconde après colonne B...")
        time.sleep(0.25)

        # ÉTAPE 13: Tab pour passer au champ suivant
        print("⌨️  Tab pour champ suivant...")
        if not send_tab(1):
            print("❌ Échec envoi Tab")
            return False

        # Attendre 0.25 seconde après le tab (0.5s / 2)
        print("⏳ Attente 0.25 seconde...")
        time.sleep(0.25)

        # ÉTAPE 14: Traitement colonne C
        if not process_column_c(code_c):
            print("⚠️  Traitement colonne C échoué, on continue...")

        # Attendre 0.25 seconde après colonne C (0.5s / 2)
        print("⏳ Attente 0.25 seconde après colonne C...")
        time.sleep(0.25)

        # ÉTAPE 15: Tab pour passer au champ final (colonne D)
        print("⌨️  Tab pour champ final (colonne D)...")
        if not send_tab(1):
            print("❌ Échec envoi Tab final")
            return False

        # Attendre 0.25 seconde après le tab final (0.5s / 2)
        print("⏳ Attente 0.25 seconde...")
        time.sleep(0.25)

        # ÉTAPE 16: Traitement final colonne D (basé sur le chiffre)
        if not process_column_d_final(code_d):
            print("⚠️  Traitement final colonne D échoué, on continue...")

        # ÉTAPE 17: Shift maintenu + 3x Tab puis Espace deux fois
        print("🎯 ÉTAPE FINALE: Shift maintenu + 3x Tab puis Espace deux fois")
        try:
            # Maintenir Shift et faire 3x Tab
            print("⌨️  Shift maintenu + 3x Tab...")
            keyboard.press(Key.shift)
            for i in range(3):
                keyboard.press(Key.tab)
                keyboard.release(Key.tab)
                time.sleep(0.25)
                print(f"⌨️  Tab {i+1}/3 (Shift maintenu)")
            keyboard.release(Key.shift)
            print("✅ Shift relâché")

            # Espace deux fois
            print("⌨️  Espace 1/2...")
            keyboard.press(Key.space)
            keyboard.release(Key.space)
            time.sleep(0.25)

            print("⌨️  Espace 2/2...")
            keyboard.press(Key.space)
            keyboard.release(Key.space)
            time.sleep(0.25)

            # Attente 14 secondes puis Ctrl+W
            print("⏳ Attente 14 secondes...")
            time.sleep(14.0)

            print("⌨️  Ctrl+W...")
            with keyboard.pressed(Key.ctrl):
                keyboard.press('w')
                keyboard.release('w')
            time.sleep(1.0)

            # Focus sur Clarify pour préparer le cas suivant
            print("🎯 Focus sur Clarify pour le cas suivant...")
            try:
                windows = find_clarify_window()
                if windows:
                    hwnd = windows[0][0]
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(0.5)
                    print("✅ Focus Clarify restauré")
                else:
                    print("⚠️  Clarify non trouvé pour focus")
            except Exception as e:
                print(f"⚠️  Erreur focus Clarify: {e}")

            print("✅ Étape finale terminée avec succès!")

        except Exception as e:
            print(f"❌ Erreur étape finale: {e}")
            # S'assurer que Shift est relâché en cas d'erreur
            try:
                keyboard.release(Key.shift)
            except:
                pass

        print(f"✅ Cas {case_id} traité avec succès!")
        return True

    except Exception as e:
        print(f"❌ Erreur traitement cas {case_id}: {e}")
        return False

def main():
    """Fonction principale - Lance l'interface graphique"""

    print("🚀 CLARIFY - AUTOMATISATION AVEC PYNPUT")
    print("=" * 50)

    # Vérifier les dépendances
    if not check_dependencies():
        print("\n❌ Impossible de continuer sans les dépendances requises.")
        return

    # Vérifier pynput spécifiquement
    if not PYNPUT_AVAILABLE:
        print("\n❌ pynput n'est pas disponible!")
        print("📦 Installez avec: pip install pynput")
        return

    print("\n🎉 Lancement de l'interface graphique...")

    try:
        # Lancer l'interface graphique
        from clarify_gui_pynput import main as gui_main
        gui_main()

    except ImportError as e:
        print(f"\n❌ Erreur d'importation: {e}")
        print("Vérifiez que clarify_gui_pynput.py est présent et correct.")

        # Fallback vers mode console
        print("\n🔄 Basculement vers le mode console...")
        run_console_mode()

    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")

def run_console_mode():
    """Mode console de secours"""

    # Initialiser le contrôleur clavier
    global keyboard
    keyboard = KeyboardController()

    # Proposer le mode test
    print("\n🧪 MODE TEST DISPONIBLE")
    print("Voulez-vous tester uniquement Ctrl+Shift+Y sur C34131148 ? (o/n)")
    choice = input("Choix: ").lower().strip()

    if choice in ['o', 'oui', 'y', 'yes']:
        test_ctrl_shift_y()
        return

    # Vérifier les fichiers
    if not check_files():
        print("\n❌ Impossible de continuer sans le fichier TT_liste.xlsx.")
        return

    # Lire les données depuis Excel (IDs + codes D)
    cases_data = read_excel_data()
    if not cases_data:
        print("\n❌ Aucune donnée trouvée dans TT_liste.xlsx")
        return

    # Rechercher Clarify
    print(f"\n🔍 Recherche de Clarify...")
    windows = find_clarify_window()
    if not windows:
        print("❌ Clarify non trouvé!")
        print("💡 Assurez-vous que Clarify est ouvert")
        return

    # Utiliser la première fenêtre trouvée
    hwnd, title = windows[0]
    print(f"✅ Clarify trouvé: {title}")

    # Activer Clarify
    if not activate_clarify_window(hwnd):
        print("❌ Impossible d'activer Clarify")
        return

    # Information avant démarrage automatique
    print(f"\n📋 PRÊT À TRAITER {len(cases_data)} CAS")
    print("💡 Assurez-vous que:")
    print("   - Clarify est au premier plan")
    print("   - Vous êtes dans le bon module/écran")
    print("   - Aucune boîte de dialogue n'est ouverte")
    print(f"\n🚀 Démarrage automatique du traitement...")
    time.sleep(2)  # Pause de 2 secondes pour laisser le temps de lire

    # Traitement des cas
    print(f"\n🎯 DÉBUT DU TRAITEMENT")
    print("=" * 50)

    success_count = 0
    failed_count = 0

    for i, case_data in enumerate(cases_data, 1):
        if process_single_case(case_data, i, len(cases_data), hwnd):
            success_count += 1
        else:
            failed_count += 1

        # Pause entre les cas (sauf pour le dernier)
        if i < len(cases_data):
            print("⏸️  Pause 3 secondes avant le cas suivant...")
            time.sleep(3)

    # Résumé final
    print(f"\n🎉 TRAITEMENT TERMINÉ!")
    print("=" * 50)
    print(f"✅ Cas réussis: {success_count}")
    print(f"❌ Cas échoués: {failed_count}")
    print(f"📊 Total traité: {success_count + failed_count}/{len(cases_data)}")

    if failed_count > 0:
        print(f"\n💡 Vérifiez les cas échoués dans Clarify")

    print(f"\n✅ Traitement terminé!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 Arrêt demandé par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
