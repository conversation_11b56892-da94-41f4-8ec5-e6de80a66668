import win32gui
import win32ui
import win32con
from ctypes import windll
import time
import pyautogui
from PIL import Image
import cv2
import numpy as np
import os
import pandas as pd
import threading
from datetime import datetime

# Variable globale pour l'arrêt d'urgence
STOP_REQUESTED = False

def find_clarify_window():
    """Trouve la fenêtre Clarify"""
    def enum_windows_callback(hwnd, windows):
        if win32gui.IsWindowVisible(hwnd):
            window_text = win32gui.GetWindowText(hwnd)
            if "Amdocs CRM - ClearSupport" in window_text or "CLARIFY" in window_text:
                windows.append((hwnd, window_text))
        return True
     
    windows = []
    win32gui.EnumWindows(enum_windows_callback, windows)
    return windows

def activate_clarify(hwnd):
    """Active Clarify en VRAI plein écran (sans bordures ni barre de tâches)"""
    try:
        print("🔄 ACTIVATION CLARIFY - VRAI PLEIN ÉCRAN...")

        # Obtenir la taille de l'écran
        import tkinter as tk
        root = tk.Tk()
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        root.destroy()

        print(f"   🖥️  Écran: {screen_width}x{screen_height}")

        # ÉTAPE 1: Forcer au premier plan AGRESSIVEMENT
        print("   🎯 Mise au premier plan FORCÉE...")
        try:
            import ctypes

            # Méthode 1: Clic pour activer
            rect = win32gui.GetWindowRect(hwnd)
            center_x = rect[0] + (rect[2] - rect[0]) // 2
            center_y = rect[1] + (rect[3] - rect[1]) // 2
            pyautogui.click(center_x, center_y)
            time.sleep(0.2)

            # Méthode 2: Attachement de threads pour contourner les restrictions
            current_thread = ctypes.windll.kernel32.GetCurrentThreadId()
            target_thread = ctypes.windll.user32.GetWindowThreadProcessId(hwnd, None)

            if target_thread != current_thread:
                # Attacher les threads
                ctypes.windll.user32.AttachThreadInput(current_thread, target_thread, True)

                # Forcer au premier plan
                win32gui.SetForegroundWindow(hwnd)
                win32gui.BringWindowToTop(hwnd)

                # Détacher les threads
                ctypes.windll.user32.AttachThreadInput(current_thread, target_thread, False)
            else:
                win32gui.SetForegroundWindow(hwnd)
                win32gui.BringWindowToTop(hwnd)

            time.sleep(0.3)

            # Méthode 3: SetActiveWindow si possible
            try:
                win32gui.SetActiveWindow(hwnd)
            except:
                pass

            # Méthode 4: Simulation Alt+Tab pour forcer le focus
            print("   🔄 Simulation Alt+Tab...")
            pyautogui.hotkey('alt', 'tab')
            time.sleep(0.5)

            # Re-forcer après Alt+Tab
            win32gui.SetForegroundWindow(hwnd)
            win32gui.BringWindowToTop(hwnd)
            time.sleep(0.3)

            # Vérifier si maintenant au premier plan
            foreground_hwnd = win32gui.GetForegroundWindow()
            if foreground_hwnd == hwnd:
                print("   ✅ Premier plan forcé avec succès!")
            else:
                print("   ⚠️  Toujours pas au premier plan, on continue...")

                # Méthode 5: Clic multiple + activation
                for i in range(3):
                    pyautogui.click(center_x, center_y)
                    time.sleep(0.1)
                    win32gui.SetForegroundWindow(hwnd)
                    time.sleep(0.2)

                    foreground_hwnd = win32gui.GetForegroundWindow()
                    if foreground_hwnd == hwnd:
                        print(f"   ✅ Premier plan réussi après {i+1} tentatives!")
                        break

        except Exception as e:
            print(f"   ⚠️  Erreur premier plan: {e}")
            # Fallback simple
            try:
                win32gui.SetForegroundWindow(hwnd)
                win32gui.BringWindowToTop(hwnd)
            except:
                pass

        # ÉTAPE 2: VRAI PLEIN ÉCRAN avec SetWindowPos
        print("   📏 VRAI plein écran (sans bordures)...")
        try:
            import ctypes

            # Constantes Windows
            SWP_SHOWWINDOW = 0x0040
            SWP_FRAMECHANGED = 0x0020
            HWND_TOP = 0

            # Supprimer les bordures et la barre de titre
            GWL_STYLE = -16
            WS_VISIBLE = 0x10000000
            WS_POPUP = 0x80000000

            # Obtenir le style actuel
            current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_STYLE)
            print(f"   📋 Style actuel: {hex(current_style)}")

            # Nouveau style : popup sans bordures
            new_style = WS_POPUP | WS_VISIBLE

            # Appliquer le nouveau style
            result = ctypes.windll.user32.SetWindowLongW(hwnd, GWL_STYLE, new_style)
            print(f"   🔧 Nouveau style appliqué: {hex(new_style)} (result: {result})")

            # Positionner la fenêtre en plein écran SANS bordures
            result = ctypes.windll.user32.SetWindowPos(
                hwnd,                    # handle de la fenêtre
                HWND_TOP,               # au-dessus de toutes les fenêtres
                0, 0,                   # position (0,0)
                screen_width,           # largeur écran complet
                screen_height,          # hauteur écran complet
                SWP_SHOWWINDOW | SWP_FRAMECHANGED  # flags
            )
            print(f"   📐 SetWindowPos result: {result}")

            time.sleep(0.5)

            # Re-forcer au premier plan
            win32gui.SetForegroundWindow(hwnd)
            win32gui.BringWindowToTop(hwnd)
            time.sleep(0.3)

        except Exception as e:
            print(f"   ❌ Erreur plein écran: {e}")
            # Fallback : maximisation normale
            print("   🔄 Fallback: maximisation normale...")
            win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)
            time.sleep(0.5)

        # ÉTAPE 3: Vérification finale
        final_rect = win32gui.GetWindowRect(hwnd)
        final_width = final_rect[2] - final_rect[0]
        final_height = final_rect[3] - final_rect[1]

        foreground_hwnd = win32gui.GetForegroundWindow()
        is_foreground = foreground_hwnd == hwnd

        coverage_w = (final_width / screen_width) * 100
        coverage_h = (final_height / screen_height) * 100

        print(f"   📊 Résultat: {final_width}x{final_height} ({coverage_w:.0f}%x{coverage_h:.0f}%)")
        print(f"   📍 Position: ({final_rect[0]}, {final_rect[1]})")
        print(f"   🎯 Premier plan: {'✅' if is_foreground else '❌'}")

        # Vérifier si c'est un VRAI plein écran
        is_true_fullscreen = (
            final_rect[0] == 0 and
            final_rect[1] == 0 and
            final_width >= screen_width and
            final_height >= screen_height
        )

        # ÉTAPE 4: Forçage final du premier plan si nécessaire
        if not is_foreground:
            print("   🔧 FORÇAGE FINAL DU PREMIER PLAN...")
            try:
                import ctypes

                # Méthode ultime 1: ShowWindow avec SW_SHOW puis activation
                win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                time.sleep(0.2)

                # Méthode ultime 2: Clic au centre + activation multiple
                center_x = final_rect[0] + final_width // 2
                center_y = final_rect[1] + final_height // 2

                for attempt in range(5):
                    pyautogui.click(center_x, center_y)
                    time.sleep(0.1)

                    # Attachement de threads
                    current_thread = ctypes.windll.kernel32.GetCurrentThreadId()
                    target_thread = ctypes.windll.user32.GetWindowThreadProcessId(hwnd, None)

                    if target_thread != current_thread:
                        ctypes.windll.user32.AttachThreadInput(current_thread, target_thread, True)
                        win32gui.SetForegroundWindow(hwnd)
                        win32gui.BringWindowToTop(hwnd)
                        ctypes.windll.user32.AttachThreadInput(current_thread, target_thread, False)
                    else:
                        win32gui.SetForegroundWindow(hwnd)
                        win32gui.BringWindowToTop(hwnd)

                    time.sleep(0.2)

                    # Vérifier si ça a marché
                    foreground_hwnd = win32gui.GetForegroundWindow()
                    if foreground_hwnd == hwnd:
                        print(f"   ✅ Premier plan forcé après {attempt + 1} tentatives!")
                        is_foreground = True
                        break

                if not is_foreground:
                    print("   ⚠️  Impossible de forcer au premier plan")
                    print("   💡 Cliquez manuellement sur Clarify pour le mettre au premier plan")

            except Exception as e:
                print(f"   ❌ Erreur forçage final: {e}")

        # Résultat final
        if is_true_fullscreen and is_foreground:
            print("   ✅ VRAI PLEIN ÉCRAN RÉUSSI! (Sans bordures + Premier plan)")
        elif coverage_w >= 95 and coverage_h >= 95 and is_foreground:
            print("   ✅ PLEIN ÉCRAN RÉUSSI! (Avec bordures + Premier plan)")
        elif is_true_fullscreen:
            print("   ⚠️  VRAI plein écran OK, mais PAS au premier plan")
            print("   💡 Cliquez sur Clarify pour le mettre au premier plan")
        elif coverage_w >= 95 and coverage_h >= 95:
            print("   ⚠️  Plein écran OK, mais PAS au premier plan")
            print("   💡 Cliquez sur Clarify pour le mettre au premier plan")
        else:
            print("   ❌ Plein écran ET premier plan échoués")
            print("   💡 Essayez F11 dans Clarify pour le mode plein écran")

        return True
        
    except Exception as e:
        print(f"\n❌ ERREUR CRITIQUE: {e}")
        return True  # Continuer quand même

def capture_and_click(hwnd, template_path):
    """Capture, détecte et clique en une fois"""
    
    try:
        # Capture rapide
        hwndDC = win32gui.GetWindowDC(hwnd)
        mfcDC = win32ui.CreateDCFromHandle(hwndDC)
        saveDC = mfcDC.CreateCompatibleDC()
        
        rect = win32gui.GetWindowRect(hwnd)
        width = rect[2] - rect[0]
        height = rect[3] - rect[1]
        
        saveBitMap = win32ui.CreateBitmap()
        saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
        saveDC.SelectObject(saveBitMap)
        
        result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 3)
        
        if result:
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']), 
                                 bmpstr, 'raw', 'BGRX', 0, 1)
            
            # Nettoyage immédiat
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, hwndDC)
            
            # Conversion pour détection
            screenshot_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            template = cv2.imread(template_path)
            
            if template is not None:
                # Détection
                screenshot_gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
                
                result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)
                
                # Seuils multiples
                if max_val >= 0.5:  # Seuil assez bas pour être sûr
                    template_h, template_w = template_gray.shape
                    center_x = max_loc[0] + template_w // 2
                    center_y = max_loc[1] + template_h // 2
                    
                    # Clic immédiat
                    window_x, window_y = rect[0], rect[1]
                    abs_x = window_x + center_x
                    abs_y = window_y + center_y
                    
                    pyautogui.click(abs_x, abs_y)
                    return True
            
        return False
        
    except:
        return False

def detect_and_click_image(hwnd, template_path, description="image"):
    """Détecte et clique sur une image template"""
    
    if not os.path.exists(template_path):
        print(f"❌ Template {description} non trouvé: {template_path}")
        return False
    
    try:
        print(f"🎯 Recherche de {description}...")
        
        # Capture rapide
        hwndDC = win32gui.GetWindowDC(hwnd)
        mfcDC = win32ui.CreateDCFromHandle(hwndDC)
        saveDC = mfcDC.CreateCompatibleDC()
        
        rect = win32gui.GetWindowRect(hwnd)
        width = rect[2] - rect[0]
        height = rect[3] - rect[1]
        
        saveBitMap = win32ui.CreateBitmap()
        saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
        saveDC.SelectObject(saveBitMap)
        
        result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 3)
        
        if result:
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']), 
                                 bmpstr, 'raw', 'BGRX', 0, 1)
            
            # Nettoyage
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, hwndDC)
            
            # Conversion pour détection
            screenshot_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            template = cv2.imread(template_path)
            
            if template is not None:
                # Détection
                screenshot_gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
                
                result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)
                
                print(f"📊 Score détection {description}: {max_val:.3f}")
                
                # Seuils pour différents types d'images
                thresholds = [0.7, 0.6, 0.5, 0.4]
                
                for threshold in thresholds:
                    if max_val >= threshold:
                        template_h, template_w = template_gray.shape
                        center_x = max_loc[0] + template_w // 2
                        center_y = max_loc[1] + template_h // 2
                        
                        print(f"✅ {description} détecté! (seuil: {threshold})")
                        print(f"   Position: ({center_x}, {center_y})")
                        
                        # Clic
                        window_x, window_y = rect[0], rect[1]
                        abs_x = window_x + center_x
                        abs_y = window_y + center_y
                        
                        print(f"👆 Clic sur {description}...")
                        pyautogui.click(abs_x, abs_y)
                        time.sleep(1)
                        
                        return True
                
                print(f"❌ {description} non détecté (score max: {max_val:.3f})")
                return False
            else:
                print(f"❌ Impossible de charger le template {description}")
                return False
        else:
            print("❌ Échec capture pour détection")
            return False
        
    except Exception as e:
        print(f"❌ Erreur détection {description}: {e}")
        return False

def create_tracking_file():
    """Crée le fichier Excel de suivi s'il n'existe pas"""

    tracking_file = "Clarify_Suivi_Traitement.xlsx"

    if not os.path.exists(tracking_file):
        print("📊 Création du fichier de suivi...")

        # Créer le DataFrame initial
        df = pd.DataFrame(columns=[
            'ID_Cas',
            'Statut',
            'Date_Debut',
            'Date_Fin',
            'Duree_Seconde',
            'Tentative',
            'Erreur_Details',
            'Session_ID'
        ])

        # Sauvegarder
        df.to_excel(tracking_file, index=False)
        print(f"✅ Fichier de suivi créé: {tracking_file}")
    else:
        print(f"📊 Fichier de suivi existant: {tracking_file}")

    return tracking_file

def get_session_id():
    """Génère un ID unique pour cette session"""
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def update_tracking_status(tracking_file, case_id, status, error_details="", session_id="", start_time=None):
    """Met à jour le statut d'un cas dans le fichier de suivi"""

    try:
        # Lire le fichier existant
        if os.path.exists(tracking_file):
            df = pd.read_excel(tracking_file)
        else:
            df = pd.DataFrame(columns=[
                'ID_Cas', 'Statut', 'Date_Debut', 'Date_Fin',
                'Duree_Seconde', 'Tentative', 'Erreur_Details', 'Session_ID'
            ])

        current_time = datetime.now()

        # Chercher si le cas existe déjà dans cette session
        mask = (df['ID_Cas'] == case_id) & (df['Session_ID'] == session_id)
        existing_row = df[mask]

        if len(existing_row) > 0:
            # Mettre à jour la ligne existante
            index = existing_row.index[0]
            df.loc[index, 'Statut'] = status
            df.loc[index, 'Date_Fin'] = current_time
            df.loc[index, 'Erreur_Details'] = error_details

            # Calculer la durée si on a une date de début
            if pd.notna(df.loc[index, 'Date_Debut']):
                debut = pd.to_datetime(df.loc[index, 'Date_Debut'])
                duree = (current_time - debut).total_seconds()
                df.loc[index, 'Duree_Seconde'] = duree
        else:
            # Ajouter une nouvelle ligne
            tentative = len(df[df['ID_Cas'] == case_id]) + 1

            new_row = {
                'ID_Cas': case_id,
                'Statut': status,
                'Date_Debut': start_time if start_time else current_time,
                'Date_Fin': current_time if status in ['SUCCESS', 'ECHEC', 'INTERROMPU'] else None,
                'Duree_Seconde': None,
                'Tentative': tentative,
                'Erreur_Details': error_details,
                'Session_ID': session_id
            }

            if start_time and status in ['SUCCESS', 'ECHEC', 'INTERROMPU']:
                duree = (current_time - start_time).total_seconds()
                new_row['Duree_Seconde'] = duree

            df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)

        # Sauvegarder immédiatement
        df.to_excel(tracking_file, index=False)

        # Log pour debug
        print(f"📝 Suivi: {case_id} -> {status}")

    except Exception as e:
        print(f"⚠️  Erreur mise à jour suivi: {e}")

def read_excel_ids(excel_path):
    """Lit les IDs depuis le fichier Excel colonne A"""

    if not os.path.exists(excel_path):
        print(f"❌ Fichier Excel non trouvé: {excel_path}")
        return []

    try:
        print(f"📖 Lecture du fichier: {excel_path}")

        # Lire le fichier Excel
        df = pd.read_excel(excel_path, usecols='A', header=None)

        # Extraire la colonne A et enlever les valeurs vides
        ids = df[0].dropna().astype(str).tolist()

        # Enlever les espaces et filtrer les valeurs vides
        ids = [id_val.strip() for id_val in ids if str(id_val).strip() and str(id_val).strip().lower() != 'nan']

        print(f"✅ {len(ids)} ID(s) trouvé(s):")
        for i, case_id in enumerate(ids, 1):
            print(f"   {i}. {case_id}")

        return ids

    except Exception as e:
        print(f"❌ Erreur lecture Excel: {e}")
        return []

def get_unprocessed_cases(tracking_file, case_ids, session_id):
    """Retourne la liste des cas non encore traités avec succès"""

    try:
        if not os.path.exists(tracking_file):
            return case_ids

        df = pd.read_excel(tracking_file)

        # Cas ayant réussi (toutes sessions confondues)
        successful_cases = df[df['Statut'] == 'SUCCESS']['ID_Cas'].unique().tolist()

        # Cas à traiter = tous les cas - cas réussis
        remaining_cases = [case_id for case_id in case_ids if case_id not in successful_cases]

        if len(successful_cases) > 0:
            print(f"📋 Cas déjà traités avec succès: {len(successful_cases)}")
            for case in successful_cases:
                print(f"   ✅ {case}")

        if len(remaining_cases) < len(case_ids):
            print(f"🔄 Reprise du traitement: {len(remaining_cases)} cas restants")

        return remaining_cases

    except Exception as e:
        print(f"⚠️  Erreur lecture suivi: {e}")
        return case_ids

def check_stop_requested(action_name=""):
    """Vérifie si l'arrêt a été demandé"""
    global STOP_REQUESTED

    if STOP_REQUESTED:
        print(f"\n🛑 ARRÊT DÉTECTÉ pendant: {action_name}")
        print("   Nettoyage en cours...")
        return True
    return False

def escape_key_listener():
    """Écoute la touche Échap en arrière-plan"""
    global STOP_REQUESTED

    try:
        import keyboard
        print("⌨️  Détecteur Échap activé (appuyez sur Échap pour arrêter)")

        while not STOP_REQUESTED:
            if keyboard.is_pressed('esc'):
                print("\n🛑 ARRÊT D'URGENCE DEMANDÉ (Échap)")
                STOP_REQUESTED = True
                break
            time.sleep(0.1)

    except ImportError:
        print("⚠️  Module 'keyboard' non installé - Échap non disponible")
        print("   Installez avec: pip install keyboard")
    except Exception as e:
        print(f"⚠️  Erreur détecteur Échap: {e}")

def process_single_case(hwnd, case_id, search_template_path, suivi_template_path, tracking_file, session_id):
    """Traite un seul cas avec suivi complet"""

    start_time = datetime.now()

    print(f"\n🔄 Traitement du cas: {case_id}")

    # Enregistrer le début du traitement
    update_tracking_status(tracking_file, case_id, "EN_COURS", "", session_id, start_time)

    try:
        # Vérifier arrêt avant de commencer
        if check_stop_requested("début traitement cas"):
            update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur (Échap)", session_id, start_time)
            return False

        # ÉTAPE 1: Clic sur icône de recherche
        print("  📍 Clic sur icône recherche...")
        if not capture_and_click(hwnd, search_template_path):
            error_msg = "Échec clic icône recherche"
            print(f"  ❌ {error_msg}")
            update_tracking_status(tracking_file, case_id, "ECHEC", error_msg, session_id, start_time)
            return False

        # Vérifier arrêt après clic
        if check_stop_requested("après clic recherche"):
            update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur après clic recherche", session_id, start_time)
            return False

        # ÉTAPE 2: Attendre ouverture fenêtre
        print("  ⏳ Attente ouverture fenêtre...")
        for i in range(30):  # 3 secondes en morceaux de 0.1s
            if check_stop_requested("attente fenêtre"):
                update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur pendant attente fenêtre", session_id, start_time)
                return False
            time.sleep(0.1)

        # ÉTAPE 3: Saisir l'ID
        print(f"  ⌨️  Saisie: {case_id}")
        try:
            pyautogui.hotkey('ctrl', 'a')  # Sélectionner tout
            time.sleep(0.2)

            if check_stop_requested("avant saisie ID"):
                update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur avant saisie", session_id, start_time)
                return False

            pyautogui.write(case_id)
            time.sleep(0.5)
        except Exception as e:
            error_msg = f"Erreur saisie ID: {e}"
            print(f"  ❌ {error_msg}")
            update_tracking_status(tracking_file, case_id, "ECHEC", error_msg, session_id, start_time)
            return False

        # ÉTAPE 4: Lancer recherche
        print("  🔍 Lancement recherche...")
        try:
            pyautogui.press('enter')
        except Exception as e:
            error_msg = f"Erreur lancement recherche: {e}"
            print(f"  ❌ {error_msg}")
            update_tracking_status(tracking_file, case_id, "ECHEC", error_msg, session_id, start_time)
            return False

        # Attendre résultats avec vérification d'arrêt
        print("  ⏳ Attente résultats...")
        for i in range(40):  # 4 secondes en morceaux de 0.1s
            if check_stop_requested("attente résultats"):
                update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur pendant attente résultats", session_id, start_time)
                return False
            time.sleep(0.1)

        # NOUVELLES ÉTAPES AJOUTÉES
        # ÉTAPE 5: Clic sur Bureau
        bureau_template_path = os.path.join("image", "bureau.png")
        if os.path.exists(bureau_template_path):
            print("  🖥️ Clic sur Bureau...")
            if not capture_and_click(hwnd, bureau_template_path):
                error_msg = "Bouton Bureau non trouvé"
                print(f"  ⚠️ {error_msg} - Continuons quand même...")
                # Ne pas retourner False, continuer le traitement
            else:
                print("  ✅ Bureau cliqué avec succès")

                # Vérifier arrêt après clic Bureau
                if check_stop_requested("après clic Bureau"):
                    update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur après clic Bureau", session_id, start_time)
                    return False

                # Attendre un peu après le clic Bureau
                time.sleep(1.0)
        else:
            print(f"  ⚠️ Template Bureau manquant: {bureau_template_path}")

        # ÉTAPE 6: Clic sur Reprendre
        reprendre_template_path = os.path.join("image", "reprendre.png")
        if os.path.exists(reprendre_template_path):
            print("  ▶️ Clic sur Reprendre...")
            if not capture_and_click(hwnd, reprendre_template_path):
                error_msg = "Bouton Reprendre non trouvé"
                print(f"  ⚠️ {error_msg} - Continuons quand même...")
                # Ne pas retourner False, continuer le traitement
            else:
                print("  ✅ Reprendre cliqué avec succès")

                # Vérifier arrêt après clic Reprendre
                if check_stop_requested("après clic Reprendre"):
                    update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur après clic Reprendre", session_id, start_time)
                    return False

                # Attendre un peu après le clic Reprendre
                time.sleep(1.0)
        else:
            print(f"  ⚠️ Template Reprendre manquant: {reprendre_template_path}")

        # ÉTAPE 7: Clic sur Accept Reprendre
        accept_reprendre_template_path = os.path.join("image", "accept_reprendre.png")
        if os.path.exists(accept_reprendre_template_path):
            print("  ✅ Clic sur Accept Reprendre...")
            if not capture_and_click(hwnd, accept_reprendre_template_path):
                error_msg = "Bouton Accept Reprendre non trouvé"
                print(f"  ⚠️ {error_msg} - Continuons quand même...")
                # Ne pas retourner False, continuer le traitement
            else:
                print("  ✅ Accept Reprendre cliqué avec succès")

                # Vérifier arrêt après clic Accept Reprendre
                if check_stop_requested("après clic Accept Reprendre"):
                    update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur après clic Accept Reprendre", session_id, start_time)
                    return False

                # Attendre un peu après le clic Accept Reprendre
                time.sleep(1.5)
        else:
            print(f"  ⚠️ Template Accept Reprendre manquant: {accept_reprendre_template_path}")

        # Attendre un peu avant de continuer vers Suivi Résolution
        print("  ⏳ Attente avant Suivi Résolution...")
        for i in range(20):  # 2 secondes supplémentaires
            if check_stop_requested("avant Suivi Résolution"):
                update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur avant Suivi Résolution", session_id, start_time)
                return False
            time.sleep(0.1)

        # ÉTAPE 5: Cliquer sur suivi résolution
        print("  🎯 Recherche 'Suivi Résolution'...")
        if check_stop_requested("avant suivi résolution"):
            update_tracking_status(tracking_file, case_id, "INTERROMPU", "Arrêt utilisateur avant suivi résolution", session_id, start_time)
            return False

        if detect_and_click_image(hwnd, suivi_template_path, "Suivi Résolution"):
            print(f"  ✅ Cas {case_id} traité avec succès!")
            update_tracking_status(tracking_file, case_id, "SUCCESS", "", session_id, start_time)

            # Délai avant le cas suivant avec vérification
            for i in range(20):  # 2 secondes en morceaux de 0.1s
                if check_stop_requested("pause entre cas"):
                    return True  # Le cas actuel est réussi même si on s'arrête
                time.sleep(0.1)

            return True
        else:
            error_msg = "Suivi Résolution non trouvé"
            print(f"  ⚠️  {error_msg}")
            update_tracking_status(tracking_file, case_id, "ECHEC", error_msg, session_id, start_time)
            return False

    except Exception as e:
        error_msg = f"Erreur inattendue: {e}"
        print(f"  ❌ {error_msg}")
        update_tracking_status(tracking_file, case_id, "ECHEC", error_msg, session_id, start_time)
        return False

def main():
    """TRAITEMENT EN MASSE avec suivi Excel complet - VERSION SIMPLE"""

    global STOP_REQUESTED

    print("🚀 TRAITEMENT EN MASSE CLARIFY - VERSION SIMPLE")
    print("=" * 60)
    print("💡 ASTUCE: Appuyez sur ÉCHAP à tout moment pour arrêter")
    print("📊 SUIVI: Tout est enregistré dans un fichier Excel")
    print("=" * 60)

    # Générer ID de session unique
    session_id = get_session_id()
    print(f"🆔 Session ID: {session_id}")

    # Créer/charger le fichier de suivi
    tracking_file = create_tracking_file()

    # Démarrer le détecteur d'Échap en arrière-plan
    escape_thread = threading.Thread(target=escape_key_listener, daemon=True)
    escape_thread.start()
    time.sleep(0.5)

    try:
        # Fichier Excel source
        excel_path = "TT_liste.xlsx"

        # Lire les IDs depuis Excel
        if check_stop_requested("lecture Excel"):
            return

        all_case_ids = read_excel_ids(excel_path)

        if not all_case_ids:
            print("❌ Aucun ID trouvé dans le fichier Excel")
            input("Appuyez sur Entrée pour quitter...")
            return

        # Déterminer les cas à traiter (exclure ceux déjà réussis)
        case_ids = get_unprocessed_cases(tracking_file, all_case_ids, session_id)

        if len(case_ids) == 0:
            print("🎉 TOUS LES CAS ONT DÉJÀ ÉTÉ TRAITÉS AVEC SUCCÈS!")
            input("Appuyez sur Entrée pour quitter...")
            return

        if check_stop_requested("après lecture Excel"):
            return

        # Templates
        search_template_path = os.path.join("image", "search_icon_selected.png")
        suivi_template_path = os.path.join("image", "suivi_resolution.png")

        # Vérifier templates
        if not os.path.exists(search_template_path):
            print(f"❌ Template recherche manquant: {search_template_path}")
            input("Appuyez sur Entrée pour quitter...")
            return

        if not os.path.exists(suivi_template_path):
            print(f"❌ Template suivi résolution manquant: {suivi_template_path}")
            input("Appuyez sur Entrée pour quitter...")
            return

        if check_stop_requested("vérification templates"):
            return

        # Trouver Clarify
        print(f"\n🔍 Recherche de Clarify...")
        windows = find_clarify_window()
        if not windows:
            print("❌ Clarify non trouvé")
            input("Appuyez sur Entrée pour quitter...")
            return

        hwnd = windows[0][0]
        print(f"✅ Clarify trouvé: {windows[0][1]}")

        if check_stop_requested("après recherche Clarify"):
            return

        # Activer Clarify en plein écran et premier plan
        print(f"\n🖥️  Activation de Clarify en plein écran...")
        activate_clarify(hwnd)

        if check_stop_requested("après activation Clarify"):
            return

        # Traitement en boucle avec suivi
        print(f"\n🎯 DÉBUT DU TRAITEMENT EN MASSE")
        print(f"📋 {len(case_ids)} cas à traiter dans cette session")
        print(f"📊 Suivi en temps réel: {tracking_file}")
        print(f"⚠️  Appuyez sur ÉCHAP pour arrêter à tout moment")

        success_count = 0
        failed_count = 0
        stopped_early = False
        current_case = None

        for i, case_id in enumerate(case_ids, 1):
            if check_stop_requested("début boucle"):
                stopped_early = True
                break

            current_case = case_id

            print(f"\n{'='*30}")
            print(f"CAS {i}/{len(case_ids)}: {case_id}")
            print(f"{'='*30}")

            if process_single_case(hwnd, case_id, search_template_path, suivi_template_path, tracking_file, session_id):
                success_count += 1
            else:
                failed_count += 1

            # Si arrêt demandé pendant le traitement
            if STOP_REQUESTED:
                stopped_early = True
                break

            # Petite pause entre les cas avec vérification
            if i < len(case_ids):
                print(f"⏸️  Pause avant le cas suivant...")
                for j in range(20):  # 2 secondes en morceaux de 0.1s
                    if check_stop_requested("pause entre cas"):
                        stopped_early = True
                        break
                    time.sleep(0.1)

                if stopped_early:
                    break

        # Résumé final avec suivi Excel
        print(f"\n" + "="*60)
        if stopped_early:
            print(f"🛑 TRAITEMENT INTERROMPU (Échap)")
            print(f"📊 RÉSUMÉ PARTIEL SESSION {session_id}:")
            processed_count = i if current_case else i-1
        else:
            print(f"🎉 TRAITEMENT TERMINÉ!")
            print(f"📊 RÉSUMÉ COMPLET SESSION {session_id}:")
            processed_count = len(case_ids)

        print(f"   ✅ Réussis: {success_count}/{processed_count}")
        print(f"   ❌ Échecs: {failed_count}/{processed_count}")

        if stopped_early:
            remaining = len(case_ids) - (i if current_case else i-1)
            print(f"   ⏭️  Non traités: {remaining}")

        print(f"\n📊 FICHIER DE SUIVI: {tracking_file}")

        if stopped_early:
            print(f"\n🔄 POUR REPRENDRE:")
            print(f"   1. Relancez le script")
            print(f"   2. Les cas déjà réussis seront automatiquement ignorés")
            print(f"   3. Le traitement reprendra là où il s'est arrêté")

    except Exception as e:
        print(f"\n❌ ERREUR CRITIQUE: {e}")

    finally:
        # Arrêter le thread d'écoute
        STOP_REQUESTED = True

        print(f"\n✅ FIN DU PROGRAMME!")
        print(f"📊 Consultez {tracking_file} pour le suivi détaillé")
        input("Appuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    try:
        # Vérifier les dépendances
        import pandas as pd
    except ImportError:
        print("❌ Pandas requis: pip install pandas openpyxl")
        input("Appuyez sur Entrée pour quitter...")
        exit(1)

    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Arrêt demandé par Ctrl+C")
        STOP_REQUESTED = True
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        STOP_REQUESTED = True
        input("Appuyez sur Entrée pour quitter...")
    finally:
        STOP_REQUESTED = True
        print("👋 Au revoir!")
