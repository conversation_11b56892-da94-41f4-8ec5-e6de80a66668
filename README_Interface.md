# 🚀 Clarify - Interface de Traitement Automatique

Cette interface graphique permet de contrôler facilement le script de traitement automatique Clarify avec une console intégrée et des contrôles Start/Stop.

## 📋 Fonctionnalités

- **Interface graphique intuitive** avec boutons Start/Stop
- **Sélection de fichier Excel** via une boîte de dialogue
- **Console intégrée** pour voir les informations de traitement en temps réel
- **Contrôle d'arrêt** pour stopper le traitement à tout moment
- **Suivi automatique** dans un fichier Excel
- **Vérification des prérequis** avant le lancement

## 🛠️ Installation

### Prérequis

1. **Python 3.7+** installé sur votre système
2. **Clarify** (Amdocs CRM - ClearSupport) installé et accessible

### Dépendances Python

Les dépendances suivantes sont requises :

```bash
pip install pandas openpyxl pywin32 pyautogui opencv-python pillow
```

### Fichiers requis

Assurez-vous d'avoir tous ces fichiers dans le même dossier :

```
📁 Dossier_Clarify/
├── 📄 lancer_interface.py          # Lanceur principal
├── 📄 clarify_gui.py               # Interface graphique
├── 📄 clic_ID_2_simple.py          # Script de traitement
├── 📄 TT_liste.xlsx                # Fichier Excel avec les IDs (optionnel)
└── 📁 image/
    ├── 🖼️ search_icon_selected.png  # Template icône recherche
    └── 🖼️ suivi_resolution.png      # Template suivi résolution
```

## 🚀 Utilisation

### Lancement

1. **Double-cliquez** sur `lancer_interface.py`
   
   OU
   
2. **Ouvrez un terminal** dans le dossier et tapez :
   ```bash
   python lancer_interface.py
   ```

### Utilisation de l'interface

1. **📁 Sélection du fichier**
   - Cliquez sur "Parcourir" pour sélectionner votre fichier `TT_liste.xlsx`
   - Le fichier doit contenir les IDs à traiter dans la colonne A

2. **🎮 Contrôles**
   - **▶️ START** : Lance le traitement automatique
   - **⏹️ STOP** : Arrête le traitement en cours

3. **📟 Console**
   - Affiche toutes les informations de traitement en temps réel
   - Timestamps automatiques pour chaque action
   - Bouton "🗑️ Effacer Console" pour nettoyer l'affichage

4. **📊 Suivi**
   - Le suivi est automatiquement sauvé dans `Clarify_Suivi_Traitement.xlsx`
   - Chaque cas traité est enregistré avec son statut et sa durée

### Avant de commencer

1. **Ouvrez Clarify** (Amdocs CRM - ClearSupport)
2. **Connectez-vous** à votre session
3. **Assurez-vous** que Clarify est visible à l'écran
4. **Préparez votre fichier Excel** avec les IDs dans la colonne A

## 📊 Format du fichier Excel

Le fichier `TT_liste.xlsx` doit avoir cette structure :

```
| A        |
|----------|
| ID_001   |
| ID_002   |
| ID_003   |
| ...      |
```

- **Colonne A** : IDs des cas à traiter
- **Pas d'en-tête** requis
- **Format** : .xlsx (Excel)

## 🔧 Fonctionnement

### Processus automatique

Pour chaque ID dans le fichier Excel :

1. **🔍 Recherche** : Clic sur l'icône de recherche dans Clarify
2. **⌨️ Saisie** : Tape l'ID du cas
3. **🔍 Lancement** : Appuie sur Entrée pour lancer la recherche
4. **⏳ Attente** : Attend les résultats de la recherche
5. **🎯 Clic** : Clique sur "Suivi Résolution"
6. **📝 Suivi** : Enregistre le résultat dans le fichier de suivi

### Gestion des erreurs

- **Cas déjà traités** : Automatiquement ignorés
- **Erreurs de traitement** : Enregistrées dans le fichier de suivi
- **Arrêt d'urgence** : Possible à tout moment avec le bouton STOP
- **Reprise** : Le traitement peut reprendre là où il s'est arrêté

## 📁 Fichiers générés

### Clarify_Suivi_Traitement.xlsx

Fichier de suivi automatique contenant :

- **ID_Cas** : Identifiant du cas traité
- **Statut** : SUCCESS, ECHEC, INTERROMPU, EN_COURS
- **Date_Debut** : Heure de début du traitement
- **Date_Fin** : Heure de fin du traitement
- **Duree_Seconde** : Durée du traitement en secondes
- **Tentative** : Numéro de tentative
- **Erreur_Details** : Détails des erreurs éventuelles
- **Session_ID** : Identifiant unique de la session

## 🆘 Dépannage

### Problèmes courants

1. **"Clarify non trouvé"**
   - Vérifiez que Clarify est ouvert
   - Le titre de la fenêtre doit contenir "Amdocs CRM - ClearSupport" ou "CLARIFY"

2. **"Template non trouvé"**
   - Vérifiez que le dossier `image/` existe
   - Vérifiez que les fichiers `.png` sont présents

3. **"Erreur de dépendance"**
   - Installez les dépendances avec `pip install pandas openpyxl pywin32 pyautogui opencv-python pillow`

4. **"Fichier Excel non lu"**
   - Vérifiez le format du fichier (.xlsx)
   - Assurez-vous que les IDs sont dans la colonne A
   - Fermez le fichier Excel s'il est ouvert dans Excel

### Logs et debug

- Tous les messages sont affichés dans la console intégrée
- Les erreurs détaillées sont sauvées dans le fichier de suivi
- En cas de plantage, un fichier `Emergency_State_*.txt` peut être créé

## 💡 Conseils d'utilisation

1. **Testez d'abord** avec quelques IDs pour vérifier le bon fonctionnement
2. **Surveillez la console** pour détecter les problèmes rapidement
3. **Utilisez STOP** si vous voyez des erreurs répétées
4. **Vérifiez le fichier de suivi** pour voir les détails des traitements
5. **Gardez Clarify au premier plan** pendant le traitement

## 📞 Support

En cas de problème :

1. Vérifiez les messages dans la console
2. Consultez le fichier `Clarify_Suivi_Traitement.xlsx`
3. Vérifiez que tous les fichiers requis sont présents
4. Redémarrez l'interface si nécessaire

---

**Version** : 1.0  
**Dernière mise à jour** : 2025-01-03
