{"// CONFIGURATION CLARIFY AUTOMATION": "Fichier de configuration pour personnaliser les étapes", "// === FICHIERS ET CHEMINS ===": "", "files": {"excel_input": "IDs_a_traiter.xlsx", "tracking_file": "Clarify_Suivi_Traitement.xlsx", "templates": {"search_icon": "image/search_icon_selected.png", "ok_01": "image/OK_01.png", "suivi_resolution": "image/suivi_resolution.png", "bureau": "image/bureau.png", "reprendre": "image/reprendre.png", "accept_reprendre": "image/accept_reprendre.png"}}, "// === FENÊTRE CLARIFY ===": "", "clarify_window": {"window_titles": ["Amdocs CRM - ClearSupport", "CLARIFY"], "activation": {"enable_fullscreen": true, "force_foreground": true, "max_activation_attempts": 5, "activation_delay": 0.3, "manual_pause_seconds": 10}}, "// === DÉLAIS ET TIMEOUTS ===": "", "timing": {"after_search_click": 3.0, "after_id_input": 0.5, "after_search_launch": 4.0, "after_successful_case": 2.0, "between_cases": 6.0, "ctrl_a_delay": 0.2, "after_click_delay": 1.0, "stop_check_interval": 0.1}, "// === DÉTECTION D'IMAGES ===": "", "image_detection": {"thresholds": [0.7, 0.6, 0.5, 0.4], "quick_threshold": 0.5, "template_matching_method": "TM_CCOEFF_NORMED", "save_debug_screenshots": false, "debug_folder": "debug_screenshots"}, "// === ÉTAPES DU PROCESSUS ===": "", "process_steps": {"step_1_search_click": {"enabled": true, "description": "Clic sur icône de recherche", "template": "search_icon", "required": true, "retry_attempts": 3, "retry_delay": 1.0}, "step_2_wait_window": {"enabled": true, "description": "Attente ouverture fenêtre", "wait_time": 3.0, "required": true}, "step_3_input_id": {"enabled": true, "description": "Saisie de l'ID", "use_ctrl_a": true, "required": true, "clear_field_first": true}, "step_4_launch_search": {"enabled": true, "description": "Lancement recherche (Entrée)", "key": "enter", "required": true}, "step_5_wait_results": {"enabled": true, "description": "Attente des résultats", "wait_time": 4.0, "required": true}, "step_6_click_ok": {"enabled": true, "description": "Clic sur OK_01", "template": "ok_01", "required": true, "retry_attempts": 3, "retry_delay": 1.0, "wait_after": 10.0}, "step_7_click_suivi": {"enabled": true, "description": "Clic sur Suivi Résolution", "template": "suivi_resolution", "required": true, "retry_attempts": 2, "retry_delay": 1.0}, "step_8_additional_clicks": {"enabled": false, "description": "Clics additionnels (bureau, reprendre, accept)", "sequence": [{"template": "bureau", "description": "Clic bureau", "wait_after": 1.0, "required": false}, {"template": "reprendre", "description": "Clic reprendre", "wait_after": 1.0, "required": false}, {"template": "accept_reprendre", "description": "Clic accepter reprendre", "wait_after": 1.0, "required": false}]}}, "// === GESTION D'ERREURS ===": "", "error_handling": {"continue_on_error": true, "max_retries_per_case": 2, "retry_delay": 5.0, "save_error_screenshots": true, "error_screenshot_folder": "error_screenshots"}, "// === SUIVI ET LOGS ===": "", "tracking": {"enable_excel_tracking": true, "enable_console_logs": true, "log_level": "INFO", "show_progress_bar": true, "auto_resume": true, "backup_tracking_file": true}, "// === CONTRÔLES UTILISATEUR ===": "", "user_controls": {"enable_escape_key": true, "enable_pause_resume": false, "confirm_before_start": true, "show_summary_at_end": true, "auto_close_on_completion": false}, "// === MODES DE FONCTIONNEMENT ===": "", "modes": {"debug_mode": false, "test_mode": false, "batch_size": 0, "start_from_case": "", "process_only_failed": false}, "// === PERSONNALISATION AVANCÉE ===": "", "advanced": {"pyautogui_settings": {"pause": 0.1, "failsafe": true, "failsafe_corner": "top-left"}, "window_capture_method": "PrintWindow", "use_multiple_detection_methods": true, "parallel_processing": false}}