"""
Convertisseur de configuration simple vers JSON
Lit clarify_config_simple.txt et génère clarify_config.json
"""

import json
import os
from typing import Dict, Any, Union

class ConfigConverter:
    """Convertit la configuration simple en JSON"""
    
    def __init__(self, simple_file: str = "clarify_config_simple.txt", json_file: str = "clarify_config.json"):
        self.simple_file = simple_file
        self.json_file = json_file
        self.config_data = {}
    
    def parse_simple_config(self) -> Dict[str, Any]:
        """Parse le fichier de configuration simple"""
        
        if not os.path.exists(self.simple_file):
            print(f"❌ Fichier de configuration simple non trouvé: {self.simple_file}")
            return {}
        
        config_values = {}
        
        try:
            with open(self.simple_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # Ignorer les commentaires et lignes vides
                    if not line or line.startswith('#'):
                        continue
                    
                    # Parser les lignes KEY = VALUE
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # Convertir les valeurs
                        config_values[key] = self.convert_value(value)
            
            print(f"✅ Configuration simple parsée: {len(config_values)} paramètres")
            return config_values
            
        except Exception as e:
            print(f"❌ Erreur lecture configuration simple: {e}")
            return {}
    
    def convert_value(self, value: str) -> Union[str, float, int, bool, list]:
        """Convertit une valeur string vers le bon type"""
        
        # Booléens
        if value.upper() in ['OUI', 'YES', 'TRUE', '1']:
            return True
        elif value.upper() in ['NON', 'NO', 'FALSE', '0']:
            return False
        
        # Listes (séparées par virgules)
        if ',' in value:
            items = [item.strip() for item in value.split(',')]
            # Essayer de convertir chaque élément
            converted_items = []
            for item in items:
                try:
                    if '.' in item:
                        converted_items.append(float(item))
                    else:
                        converted_items.append(int(item))
                except ValueError:
                    converted_items.append(item)
            return converted_items
        
        # Nombres
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # String par défaut
        return value
    
    def build_json_config(self, simple_config: Dict[str, Any]) -> Dict[str, Any]:
        """Construit la configuration JSON à partir des valeurs simples"""
        
        config = {
            "// CONFIGURATION CLARIFY AUTOMATION": "Généré automatiquement depuis clarify_config_simple.txt",
            
            "files": {
                "excel_input": simple_config.get("FICHIER_EXCEL_IDS", "IDs_a_traiter.xlsx"),
                "tracking_file": simple_config.get("FICHIER_SUIVI", "Clarify_Suivi_Traitement.xlsx"),
                "templates": {
                    "search_icon": simple_config.get("IMAGE_ICONE_RECHERCHE", "image/search_icon_selected.png"),
                    "ok_01": simple_config.get("IMAGE_OK_01", "image/OK_01.png"),
                    "suivi_resolution": simple_config.get("IMAGE_SUIVI_RESOLUTION", "image/suivi_resolution.png"),
                    "bureau": simple_config.get("IMAGE_BUREAU", "image/bureau.png"),
                    "reprendre": simple_config.get("IMAGE_REPRENDRE", "image/reprendre.png"),
                    "accept_reprendre": simple_config.get("IMAGE_ACCEPTER_REPRENDRE", "image/accept_reprendre.png")
                }
            },
            
            "clarify_window": {
                "window_titles": ["Amdocs CRM - ClearSupport", "CLARIFY"],
                "activation": {
                    "enable_fullscreen": simple_config.get("ACTIVER_PLEIN_ECRAN", True),
                    "force_foreground": simple_config.get("FORCER_PREMIER_PLAN", True),
                    "max_activation_attempts": simple_config.get("TENTATIVES_MAX_ACTIVATION", 5),
                    "activation_delay": 0.3,
                    "manual_pause_seconds": simple_config.get("PAUSE_MANUELLE_SECONDES", 10)
                }
            },
            
            "timing": {
                "after_search_click": simple_config.get("DELAI_APRES_CLIC_RECHERCHE", 3.0),
                "after_id_input": simple_config.get("DELAI_APRES_SAISIE_ID", 0.5),
                "after_search_launch": simple_config.get("DELAI_APRES_LANCEMENT_RECHERCHE", 4.0),
                "after_successful_case": simple_config.get("DELAI_APRES_CAS_REUSSI", 2.0),
                "between_cases": simple_config.get("DELAI_ENTRE_CAS", 2.0),
                "ctrl_a_delay": simple_config.get("DELAI_CTRL_A", 0.2),
                "after_click_delay": simple_config.get("DELAI_APRES_CLIC", 1.0),
                "stop_check_interval": 0.1
            },
            
            "image_detection": {
                "thresholds": simple_config.get("SEUILS_MULTIPLES", [0.7, 0.6, 0.5, 0.4]),
                "quick_threshold": simple_config.get("SEUIL_DETECTION_RAPIDE", 0.5),
                "template_matching_method": "TM_CCOEFF_NORMED",
                "save_debug_screenshots": simple_config.get("MODE_DEBUG", False),
                "debug_folder": "debug_screenshots"
            },
            
            "process_steps": {
                "step_1_search_click": {
                    "enabled": simple_config.get("ETAPE_CLIC_RECHERCHE_ACTIVE", True),
                    "description": "Clic sur icône de recherche",
                    "template": "search_icon",
                    "required": simple_config.get("ETAPE_CLIC_RECHERCHE_OBLIGATOIRE", True),
                    "retry_attempts": simple_config.get("ETAPE_CLIC_RECHERCHE_TENTATIVES", 3),
                    "retry_delay": 1.0
                },
                "step_2_wait_window": {
                    "enabled": simple_config.get("ETAPE_ATTENTE_FENETRE_ACTIVE", True),
                    "description": "Attente ouverture fenêtre",
                    "wait_time": simple_config.get("ETAPE_ATTENTE_FENETRE_DUREE", 3.0),
                    "required": True
                },
                "step_3_input_id": {
                    "enabled": simple_config.get("ETAPE_SAISIE_ID_ACTIVE", True),
                    "description": "Saisie de l'ID",
                    "use_ctrl_a": simple_config.get("ETAPE_SAISIE_ID_UTILISER_CTRL_A", True),
                    "required": True,
                    "clear_field_first": simple_config.get("ETAPE_SAISIE_ID_VIDER_CHAMP", True)
                },
                "step_4_launch_search": {
                    "enabled": simple_config.get("ETAPE_LANCEMENT_RECHERCHE_ACTIVE", True),
                    "description": "Lancement recherche (Entrée)",
                    "key": simple_config.get("ETAPE_LANCEMENT_RECHERCHE_TOUCHE", "enter"),
                    "required": True
                },
                "step_5_wait_results": {
                    "enabled": simple_config.get("ETAPE_ATTENTE_RESULTATS_ACTIVE", True),
                    "description": "Attente des résultats",
                    "wait_time": simple_config.get("ETAPE_ATTENTE_RESULTATS_DUREE", 4.0),
                    "required": True
                },
                "step_6_click_ok": {
                    "enabled": simple_config.get("ETAPE_CLIC_OK_01_ACTIVE", True),
                    "description": "Clic sur OK_01",
                    "template": "ok_01",
                    "required": simple_config.get("ETAPE_CLIC_OK_01_OBLIGATOIRE", True),
                    "retry_attempts": simple_config.get("ETAPE_CLIC_OK_01_TENTATIVES", 3),
                    "retry_delay": 1.0,
                    "wait_after": simple_config.get("ETAPE_CLIC_OK_01_ATTENTE_APRES", 10.0)
                },
                "step_7_click_suivi": {
                    "enabled": simple_config.get("ETAPE_SUIVI_RESOLUTION_ACTIVE", True),
                    "description": "Clic sur Suivi Résolution",
                    "template": "suivi_resolution",
                    "required": simple_config.get("ETAPE_SUIVI_RESOLUTION_OBLIGATOIRE", True),
                    "retry_attempts": simple_config.get("ETAPE_SUIVI_RESOLUTION_TENTATIVES", 2),
                    "retry_delay": 1.0
                },
                "step_8_additional_clicks": {
                    "enabled": simple_config.get("ETAPE_CLICS_ADDITIONNELS_ACTIVE", False),
                    "description": "Clics additionnels (bureau, reprendre, accept)",
                    "sequence": [
                        {
                            "template": "bureau",
                            "description": "Clic bureau",
                            "wait_after": 1.0,
                            "required": False
                        },
                        {
                            "template": "reprendre",
                            "description": "Clic reprendre",
                            "wait_after": 1.0,
                            "required": False
                        },
                        {
                            "template": "accept_reprendre",
                            "description": "Clic accepter reprendre",
                            "wait_after": 1.0,
                            "required": False
                        }
                    ]
                }
            },
            
            "error_handling": {
                "continue_on_error": simple_config.get("CONTINUER_SUR_ERREUR", True),
                "max_retries_per_case": simple_config.get("NOMBRE_MAX_TENTATIVES_PAR_CAS", 2),
                "retry_delay": simple_config.get("DELAI_ENTRE_TENTATIVES", 5.0),
                "save_error_screenshots": simple_config.get("SAUVEGARDER_CAPTURES_ERREUR", True),
                "error_screenshot_folder": simple_config.get("DOSSIER_CAPTURES_ERREUR", "error_screenshots")
            },
            
            "tracking": {
                "enable_excel_tracking": simple_config.get("ACTIVER_SUIVI_EXCEL", True),
                "enable_console_logs": simple_config.get("ACTIVER_LOGS_CONSOLE", True),
                "log_level": simple_config.get("NIVEAU_LOG", "INFO"),
                "show_progress_bar": simple_config.get("AFFICHER_BARRE_PROGRESSION", True),
                "auto_resume": simple_config.get("REPRISE_AUTOMATIQUE", True),
                "backup_tracking_file": simple_config.get("SAUVEGARDE_FICHIER_SUIVI", True)
            },
            
            "user_controls": {
                "enable_escape_key": simple_config.get("ACTIVER_TOUCHE_ECHAP", True),
                "enable_pause_resume": False,
                "confirm_before_start": simple_config.get("CONFIRMER_AVANT_DEMARRAGE", True),
                "show_summary_at_end": simple_config.get("AFFICHER_RESUME_FIN", True),
                "auto_close_on_completion": simple_config.get("FERMER_AUTO_FIN", False)
            },
            
            "modes": {
                "debug_mode": simple_config.get("MODE_DEBUG", False),
                "test_mode": simple_config.get("MODE_TEST", False),
                "batch_size": simple_config.get("TAILLE_LOT", 0),
                "start_from_case": simple_config.get("COMMENCER_A_PARTIR_DU_CAS", ""),
                "process_only_failed": simple_config.get("TRAITER_SEULEMENT_ECHECS", False)
            },
            
            "advanced": {
                "pyautogui_settings": {
                    "pause": 0.1,
                    "failsafe": True,
                    "failsafe_corner": "top-left"
                },
                "window_capture_method": "PrintWindow",
                "use_multiple_detection_methods": True,
                "parallel_processing": False
            }
        }
        
        return config
    
    def convert(self) -> bool:
        """Effectue la conversion complète"""
        
        print(f"🔄 Conversion de {self.simple_file} vers {self.json_file}...")
        
        # Parser le fichier simple
        simple_config = self.parse_simple_config()
        if not simple_config:
            return False
        
        # Construire la configuration JSON
        json_config = self.build_json_config(simple_config)
        
        # Sauvegarder le JSON
        try:
            with open(self.json_file, 'w', encoding='utf-8') as f:
                json.dump(json_config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Configuration JSON générée: {self.json_file}")
            
            # Afficher un résumé
            self.print_conversion_summary(simple_config, json_config)
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde JSON: {e}")
            return False
    
    def print_conversion_summary(self, simple_config: Dict[str, Any], json_config: Dict[str, Any]) -> None:
        """Affiche un résumé de la conversion"""
        
        print(f"\n📋 RÉSUMÉ DE LA CONVERSION")
        print("=" * 50)
        
        # Fichiers
        files = json_config.get('files', {})
        print(f"📁 Fichier Excel: {files.get('excel_input')}")
        print(f"📊 Fichier suivi: {files.get('tracking_file')}")
        
        # Étapes activées
        steps = json_config.get('process_steps', {})
        enabled_steps = [name for name, config in steps.items() if config.get('enabled', False)]
        print(f"⚙️  Étapes activées: {len(enabled_steps)}/{len(steps)}")
        
        # Délais principaux
        timing = json_config.get('timing', {})
        print(f"⏱️  Délai entre cas: {timing.get('between_cases')}s")
        print(f"⏱️  Délai après recherche: {timing.get('after_search_launch')}s")
        
        # Modes spéciaux
        modes = json_config.get('modes', {})
        if modes.get('debug_mode'):
            print("🐛 Mode debug activé")
        if modes.get('test_mode'):
            print("🧪 Mode test activé")
        
        print(f"\n✅ Configuration prête à utiliser!")

def main():
    """Fonction principale"""
    converter = ConfigConverter()
    
    if converter.convert():
        print(f"\n🎉 Conversion réussie!")
        print(f"💡 Vous pouvez maintenant utiliser votre outil avec la nouvelle configuration.")
        print(f"💡 Pour modifier à nouveau, éditez {converter.simple_file} et relancez ce script.")
    else:
        print(f"\n❌ Échec de la conversion")
        print(f"💡 Vérifiez le fichier {converter.simple_file}")

if __name__ == "__main__":
    main()
