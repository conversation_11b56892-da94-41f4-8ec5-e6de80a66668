#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestionnaire de sauvegarde automatique pour Clarify
Crée des copies de sauvegarde des fichiers Excel pour éviter les pertes de données
"""

import os
import shutil
import pandas as pd
from datetime import datetime
import threading
import time

class BackupManager:
    """Gestionnaire de sauvegarde automatique"""
    
    def __init__(self, backup_folder="backup_excel"):
        self.backup_folder = backup_folder
        self.create_backup_folder()
        self.auto_backup_active = False
        self.backup_thread = None
        
    def create_backup_folder(self):
        """Crée le dossier de sauvegarde s'il n'existe pas"""
        if not os.path.exists(self.backup_folder):
            os.makedirs(self.backup_folder)
            print(f"📁 Dossier de sauvegarde créé: {self.backup_folder}")
    
    def create_backup(self, source_file, backup_type="manual"):
        """
        Crée une sauvegarde d'un fichier Excel
        
        Args:
            source_file (str): Chemin du fichier source
            backup_type (str): Type de sauvegarde ("manual", "auto", "session")
        
        Returns:
            str: Chemin du fichier de sauvegarde créé
        """
        if not os.path.exists(source_file):
            print(f"❌ Fichier source non trouvé: {source_file}")
            return None
        
        try:
            # Générer le nom du fichier de sauvegarde
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_name = os.path.splitext(os.path.basename(source_file))[0]
            backup_filename = f"{base_name}_{backup_type}_{timestamp}.xlsx"
            backup_path = os.path.join(self.backup_folder, backup_filename)
            
            # Copier le fichier
            shutil.copy2(source_file, backup_path)
            
            print(f"💾 Sauvegarde créée: {backup_filename}")
            return backup_path
            
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde de {source_file}: {e}")
            return None
    
    def create_session_backup(self):
        """Crée une sauvegarde de début de session"""
        print("\n🔄 Création des sauvegardes de session...")
        
        files_to_backup = [
            "TT_liste.xlsx",
            "Clarify_Suivi_Traitement.xlsx"
        ]
        
        backup_files = []
        for file_path in files_to_backup:
            if os.path.exists(file_path):
                backup_path = self.create_backup(file_path, "session")
                if backup_path:
                    backup_files.append(backup_path)
            else:
                print(f"⚠️  Fichier non trouvé pour sauvegarde: {file_path}")
        
        if backup_files:
            print(f"✅ {len(backup_files)} fichier(s) sauvegardé(s) pour cette session")
        
        return backup_files
    
    def start_auto_backup(self, interval_minutes=10):
        """
        Démarre la sauvegarde automatique périodique
        
        Args:
            interval_minutes (int): Intervalle en minutes entre les sauvegardes
        """
        if self.auto_backup_active:
            print("⚠️  Sauvegarde automatique déjà active")
            return
        
        self.auto_backup_active = True
        self.backup_thread = threading.Thread(
            target=self._auto_backup_worker,
            args=(interval_minutes,),
            daemon=True
        )
        self.backup_thread.start()
        print(f"🔄 Sauvegarde automatique démarrée (intervalle: {interval_minutes} min)")
    
    def stop_auto_backup(self):
        """Arrête la sauvegarde automatique"""
        self.auto_backup_active = False
        if self.backup_thread:
            self.backup_thread.join(timeout=1)
        print("⏹️  Sauvegarde automatique arrêtée")
    
    def _auto_backup_worker(self, interval_minutes):
        """Worker thread pour la sauvegarde automatique"""
        interval_seconds = interval_minutes * 60
        
        while self.auto_backup_active:
            time.sleep(interval_seconds)
            
            if not self.auto_backup_active:
                break
            
            # Sauvegarder uniquement le fichier de suivi s'il existe
            tracking_file = "Clarify_Suivi_Traitement.xlsx"
            if os.path.exists(tracking_file):
                self.create_backup(tracking_file, "auto")
    
    def list_backups(self, file_pattern=None):
        """
        Liste les fichiers de sauvegarde disponibles
        
        Args:
            file_pattern (str): Motif pour filtrer les fichiers (optionnel)
        
        Returns:
            list: Liste des fichiers de sauvegarde
        """
        if not os.path.exists(self.backup_folder):
            return []
        
        backup_files = []
        for filename in os.listdir(self.backup_folder):
            if filename.endswith('.xlsx'):
                if file_pattern is None or file_pattern in filename:
                    file_path = os.path.join(self.backup_folder, filename)
                    file_info = {
                        'filename': filename,
                        'path': file_path,
                        'size': os.path.getsize(file_path),
                        'modified': datetime.fromtimestamp(os.path.getmtime(file_path))
                    }
                    backup_files.append(file_info)
        
        # Trier par date de modification (plus récent en premier)
        backup_files.sort(key=lambda x: x['modified'], reverse=True)
        return backup_files
    
    def restore_backup(self, backup_file, target_file):
        """
        Restaure un fichier depuis une sauvegarde
        
        Args:
            backup_file (str): Chemin du fichier de sauvegarde
            target_file (str): Chemin du fichier cible à restaurer
        
        Returns:
            bool: True si la restauration a réussi
        """
        if not os.path.exists(backup_file):
            print(f"❌ Fichier de sauvegarde non trouvé: {backup_file}")
            return False
        
        try:
            # Créer une sauvegarde du fichier actuel avant restauration
            if os.path.exists(target_file):
                self.create_backup(target_file, "pre_restore")
            
            # Restaurer le fichier
            shutil.copy2(backup_file, target_file)
            print(f"✅ Fichier restauré: {target_file}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la restauration: {e}")
            return False
    
    def cleanup_old_backups(self, keep_days=7):
        """
        Nettoie les anciennes sauvegardes
        
        Args:
            keep_days (int): Nombre de jours à conserver
        """
        if not os.path.exists(self.backup_folder):
            return
        
        cutoff_time = datetime.now().timestamp() - (keep_days * 24 * 3600)
        deleted_count = 0
        
        for filename in os.listdir(self.backup_folder):
            if filename.endswith('.xlsx'):
                file_path = os.path.join(self.backup_folder, filename)
                if os.path.getmtime(file_path) < cutoff_time:
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                    except Exception as e:
                        print(f"⚠️  Erreur suppression {filename}: {e}")
        
        if deleted_count > 0:
            print(f"🗑️  {deleted_count} ancienne(s) sauvegarde(s) supprimée(s)")
    
    def get_backup_summary(self):
        """Retourne un résumé des sauvegardes"""
        backups = self.list_backups()
        
        summary = {
            'total_backups': len(backups),
            'total_size_mb': sum(b['size'] for b in backups) / (1024 * 1024),
            'latest_backup': backups[0]['modified'] if backups else None,
            'backup_types': {}
        }
        
        # Compter par type de sauvegarde
        for backup in backups:
            backup_type = 'unknown'
            if '_auto_' in backup['filename']:
                backup_type = 'auto'
            elif '_manual_' in backup['filename']:
                backup_type = 'manual'
            elif '_session_' in backup['filename']:
                backup_type = 'session'
            elif '_pre_restore_' in backup['filename']:
                backup_type = 'pre_restore'
            
            summary['backup_types'][backup_type] = summary['backup_types'].get(backup_type, 0) + 1
        
        return summary


def main():
    """Fonction de test du gestionnaire de sauvegarde"""
    print("🧪 Test du gestionnaire de sauvegarde")
    
    backup_manager = BackupManager()
    
    # Créer une sauvegarde de session
    backup_manager.create_session_backup()
    
    # Lister les sauvegardes
    backups = backup_manager.list_backups()
    print(f"\n📋 Sauvegardes disponibles: {len(backups)}")
    for backup in backups[:5]:  # Afficher les 5 plus récentes
        print(f"  📄 {backup['filename']} ({backup['size']} bytes)")
    
    # Afficher le résumé
    summary = backup_manager.get_backup_summary()
    print(f"\n📊 Résumé des sauvegardes:")
    print(f"  Total: {summary['total_backups']} fichiers")
    print(f"  Taille: {summary['total_size_mb']:.2f} MB")
    if summary['latest_backup']:
        print(f"  Dernière: {summary['latest_backup']}")


if __name__ == "__main__":
    main()
