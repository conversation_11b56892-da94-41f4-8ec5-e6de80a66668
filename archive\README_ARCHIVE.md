# 📁 ARCHIVE - FICHIERS OBSOLÈTES

## 🗂️ **CONTENU DE L'ARCHIVE**

### `clarify_gui_OBSOLETE_pyautogui.py`
- **Ancienne interface graphique** utilisant pyautogui
- **Remplacée par** : `clarify_gui_pynput.py` (plus fiable avec pynput)
- **Date d'archivage** : 2025-07-09
- **Raison** : pynput fonctionne mieux que pyautogui pour l'automation

## 🎯 **FICHIERS ACTUELLEMENT UTILISÉS**

### Chaîne d'exécution principale :
1. **`Lancer_Clarify_Interface.bat`** → Lance le point d'entrée
2. **`lancer_interface.py`** → Point d'entrée principal
3. **`clarify_gui_pynput.py`** → Interface graphique moderne (pynput)

### Fichiers de support :
- `clic_ID_2_simple.py` - Fonctions de base
- `config_manager.py` - Gestion configuration
- `backup_manager.py` - Gestion sauvegardes
- `Selector_icone.py` - Sélecteur d'icônes

## ⚠️ **IMPORTANT**

Les fichiers dans cette archive ne sont **PAS utilisés** par l'application actuelle.
Ils sont conservés uniquement pour référence historique.

**Ne pas** réintégrer ces fichiers sans vérification complète de compatibilité.

## 🧹 **NETTOYAGE EFFECTUÉ**

- ✅ `clarify_gui.py` archivé (obsolète - pyautogui)
- ✅ Projet nettoyé pour se concentrer sur pynput
- ✅ Chaîne d'exécution clarifiée

---
*Archive créée automatiquement le 2025-07-09*
