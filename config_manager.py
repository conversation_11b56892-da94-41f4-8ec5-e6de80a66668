"""
Gestionnaire de configuration pour l'automatisation Clarify
Charge et valide les paramètres depuis clarify_config.json
"""

import json
import os
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

@dataclass
class TimingConfig:
    """Configuration des délais"""
    after_search_click: float
    after_id_input: float
    after_search_launch: float
    after_successful_case: float
    between_cases: float
    ctrl_a_delay: float
    after_click_delay: float
    stop_check_interval: float

@dataclass
class ImageDetectionConfig:
    """Configuration de la détection d'images"""
    thresholds: List[float]
    quick_threshold: float
    template_matching_method: str
    save_debug_screenshots: bool
    debug_folder: str

@dataclass
class ProcessStepConfig:
    """Configuration d'une étape du processus"""
    enabled: bool
    description: str
    required: bool
    template: Optional[str] = None
    wait_time: Optional[float] = None
    retry_attempts: Optional[int] = None
    retry_delay: Optional[float] = None
    key: Optional[str] = None
    use_ctrl_a: Optional[bool] = None
    clear_field_first: Optional[bool] = None
    sequence: Optional[List[Dict]] = None

class ConfigManager:
    """Gestionnaire de configuration principal"""
    
    def __init__(self, config_file: str = "clarify_config.json"):
        self.config_file = config_file
        self.config = {}
        self.load_config()
    
    def load_config(self) -> None:
        """Charge la configuration depuis le fichier JSON"""
        try:
            if not os.path.exists(self.config_file):
                print(f"⚠️  Fichier de configuration non trouvé: {self.config_file}")
                print("📝 Création d'un fichier de configuration par défaut...")
                self.create_default_config()
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            print(f"✅ Configuration chargée: {self.config_file}")
            self.validate_config()
            
        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON dans {self.config_file}: {e}")
            raise
        except Exception as e:
            print(f"❌ Erreur chargement configuration: {e}")
            raise
    
    def validate_config(self) -> None:
        """Valide la configuration chargée"""
        required_sections = ['files', 'timing', 'image_detection', 'process_steps']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Section manquante dans la configuration: {section}")
        
        # Vérifier les fichiers templates
        templates = self.get_templates()
        for name, path in templates.items():
            if not os.path.exists(path):
                print(f"⚠️  Template manquant: {name} -> {path}")
    
    def get_files(self) -> Dict[str, Any]:
        """Retourne la configuration des fichiers"""
        return self.config.get('files', {})
    
    def get_templates(self) -> Dict[str, str]:
        """Retourne les chemins des templates"""
        return self.config.get('files', {}).get('templates', {})
    
    def get_timing(self) -> TimingConfig:
        """Retourne la configuration des délais"""
        timing_data = self.config.get('timing', {})
        return TimingConfig(
            after_search_click=timing_data.get('after_search_click', 3.0),
            after_id_input=timing_data.get('after_id_input', 0.5),
            after_search_launch=timing_data.get('after_search_launch', 4.0),
            after_successful_case=timing_data.get('after_successful_case', 2.0),
            between_cases=timing_data.get('between_cases', 2.0),
            ctrl_a_delay=timing_data.get('ctrl_a_delay', 0.2),
            after_click_delay=timing_data.get('after_click_delay', 1.0),
            stop_check_interval=timing_data.get('stop_check_interval', 0.1)
        )
    
    def get_image_detection(self) -> ImageDetectionConfig:
        """Retourne la configuration de détection d'images"""
        img_data = self.config.get('image_detection', {})
        return ImageDetectionConfig(
            thresholds=img_data.get('thresholds', [0.7, 0.6, 0.5, 0.4]),
            quick_threshold=img_data.get('quick_threshold', 0.5),
            template_matching_method=img_data.get('template_matching_method', 'TM_CCOEFF_NORMED'),
            save_debug_screenshots=img_data.get('save_debug_screenshots', False),
            debug_folder=img_data.get('debug_folder', 'debug_screenshots')
        )
    
    def get_process_steps(self) -> Dict[str, ProcessStepConfig]:
        """Retourne la configuration des étapes du processus"""
        steps_data = self.config.get('process_steps', {})
        steps = {}
        
        for step_name, step_data in steps_data.items():
            steps[step_name] = ProcessStepConfig(
                enabled=step_data.get('enabled', True),
                description=step_data.get('description', ''),
                required=step_data.get('required', True),
                template=step_data.get('template'),
                wait_time=step_data.get('wait_time'),
                retry_attempts=step_data.get('retry_attempts'),
                retry_delay=step_data.get('retry_delay'),
                key=step_data.get('key'),
                use_ctrl_a=step_data.get('use_ctrl_a'),
                clear_field_first=step_data.get('clear_field_first'),
                sequence=step_data.get('sequence')
            )
        
        return steps
    
    def get_clarify_window_config(self) -> Dict[str, Any]:
        """Retourne la configuration de la fenêtre Clarify"""
        return self.config.get('clarify_window', {})
    
    def get_error_handling(self) -> Dict[str, Any]:
        """Retourne la configuration de gestion d'erreurs"""
        return self.config.get('error_handling', {})
    
    def get_tracking_config(self) -> Dict[str, Any]:
        """Retourne la configuration du suivi"""
        return self.config.get('tracking', {})
    
    def get_user_controls(self) -> Dict[str, Any]:
        """Retourne la configuration des contrôles utilisateur"""
        return self.config.get('user_controls', {})
    
    def get_modes(self) -> Dict[str, Any]:
        """Retourne la configuration des modes"""
        return self.config.get('modes', {})
    
    def get_advanced(self) -> Dict[str, Any]:
        """Retourne la configuration avancée"""
        return self.config.get('advanced', {})
    
    def is_step_enabled(self, step_name: str) -> bool:
        """Vérifie si une étape est activée"""
        steps = self.get_process_steps()
        return steps.get(step_name, ProcessStepConfig(enabled=False, description='', required=False)).enabled
    
    def get_template_path(self, template_name: str) -> Optional[str]:
        """Retourne le chemin d'un template"""
        templates = self.get_templates()
        return templates.get(template_name)
    
    def create_default_config(self) -> None:
        """Crée un fichier de configuration par défaut"""
        # Cette méthode serait appelée si le fichier n'existe pas
        # Pour l'instant, on assume que le fichier existe déjà
        pass
    
    def save_config(self) -> None:
        """Sauvegarde la configuration actuelle"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"✅ Configuration sauvegardée: {self.config_file}")
        except Exception as e:
            print(f"❌ Erreur sauvegarde configuration: {e}")
    
    def update_config(self, section: str, key: str, value: Any) -> None:
        """Met à jour une valeur de configuration"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
    
    def print_config_summary(self) -> None:
        """Affiche un résumé de la configuration"""
        print("\n📋 RÉSUMÉ DE LA CONFIGURATION")
        print("=" * 50)
        
        # Fichiers
        files = self.get_files()
        print(f"📁 Fichier Excel: {files.get('excel_input', 'N/A')}")
        print(f"📊 Fichier suivi: {files.get('tracking_file', 'N/A')}")
        
        # Templates
        templates = self.get_templates()
        print(f"🖼️  Templates: {len(templates)} fichier(s)")
        for name, path in templates.items():
            status = "✅" if os.path.exists(path) else "❌"
            print(f"   {status} {name}: {path}")
        
        # Étapes activées
        steps = self.get_process_steps()
        enabled_steps = [name for name, config in steps.items() if config.enabled]
        print(f"⚙️  Étapes activées: {len(enabled_steps)}/{len(steps)}")
        for step_name in enabled_steps:
            step = steps[step_name]
            print(f"   ✅ {step.description}")
        
        # Délais
        timing = self.get_timing()
        print(f"⏱️  Délai entre cas: {timing.between_cases}s")
        print(f"⏱️  Délai après recherche: {timing.after_search_launch}s")
        
        # Modes
        modes = self.get_modes()
        if modes.get('debug_mode'):
            print("🐛 Mode debug activé")
        if modes.get('test_mode'):
            print("🧪 Mode test activé")

# Instance globale pour faciliter l'utilisation
config_manager = ConfigManager()

def get_config() -> ConfigManager:
    """Retourne l'instance globale du gestionnaire de configuration"""
    return config_manager

def wait_with_config(duration_seconds: float, description: str = "", config: ConfigManager = None) -> bool:
    """Attente avec vérification d'arrêt basée sur la configuration"""
    if config is None:
        config = get_config()

    timing = config.get_timing()
    check_interval = timing.stop_check_interval

    total_checks = int(duration_seconds / check_interval)

    for i in range(total_checks):
        # Cette fonction devrait être importée depuis votre code principal
        # if check_stop_requested(description):
        #     return False
        import time
        time.sleep(check_interval)

    return True

def click_with_retry(hwnd, template_name: str, description: str = "", config: ConfigManager = None) -> bool:
    """Clic avec retry basé sur la configuration"""
    if config is None:
        config = get_config()

    template_path = config.get_template_path(template_name)
    if not template_path:
        print(f"❌ Template non trouvé: {template_name}")
        return False

    steps = config.get_process_steps()
    error_config = config.get_error_handling()

    # Trouver la configuration de l'étape correspondante
    step_config = None
    for step_name, step in steps.items():
        if step.template == template_name:
            step_config = step
            break

    if not step_config:
        print(f"⚠️  Configuration d'étape non trouvée pour {template_name}")
        return False

    max_attempts = step_config.retry_attempts or 1
    retry_delay = step_config.retry_delay or 1.0

    for attempt in range(max_attempts):
        print(f"🎯 Tentative {attempt + 1}/{max_attempts}: {description or template_name}")

        # Ici vous appelleriez votre fonction detect_and_click_image
        # success = detect_and_click_image(hwnd, template_path, description)
        # if success:
        #     return True

        if attempt < max_attempts - 1:
            print(f"⏳ Attente avant nouvelle tentative: {retry_delay}s")
            wait_with_config(retry_delay, f"retry {template_name}", config)

    return False
