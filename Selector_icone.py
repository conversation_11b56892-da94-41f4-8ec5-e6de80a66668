import win32gui
import win32ui
import win32con
from ctypes import windll
import time
import tkinter as tk
from tkinter import messagebox, ttk, filedialog
from PIL import Image, ImageTk
import cv2
import numpy as np
import os

class IconSelector:
    def __init__(self, parent=None):
        self.parent = parent
        if parent:
            self.root = tk.Toplevel(parent)
            # Éviter le cycle transient/master
            try:
                self.root.transient(parent)
                self.root.grab_set()
            except tk.TclError as e:
                print(f"Avertissement fenêtre modale: {e}")
                # Continuer sans modal si erreur
        else:
            self.root = tk.Tk()

        self.root.title("🎯 Sélecteur d'icône - Clarify")

        # Configuration de résolution par défaut
        self.resolution_presets = {
            "Compact": "1000x700",
            "Standard": "1200x800",
            "Large": "1400x1000",
            "Extra Large": "1600x1200",
            "Personnalisé": "1400x1000"
        }
        self.current_resolution = "Large"

        self.root.geometry(self.resolution_presets[self.current_resolution])
        self.root.resizable(True, True)
        self.root.minsize(900, 600)  # Taille minimale ajustée

        self.screenshot_path = None
        self.selected_coords = None
        self.template_size = 20
        self.clarify_windows = []

        # Configuration des templates multiples
        self.template_types = {
            "search_icon_selected": "🔍 Icône de recherche",
            "suivi_resolution": "📋 Suivi résolution",
            "custom": "🎯 Personnalisé"
        }
        self.current_template_type = "search_icon_selected"

        # Dossier de sauvegarde des templates
        self.templates_folder = "image"
        if not os.path.exists(self.templates_folder):
            os.makedirs(self.templates_folder)

        self.setup_styles()
        self.setup_ui()
        self.refresh_clarify_windows()

        # Démarrer le rafraîchissement automatique
        self.root.after(3000, self.auto_refresh_windows)

    def setup_styles(self):
        """Configure les styles pour une interface agrandie"""
        style = ttk.Style()

        # Style pour les boutons principaux
        style.configure('Large.TButton',
                       font=('Arial', 11, 'bold'),
                       padding=(10, 8))

        # Style pour les boutons secondaires
        style.configure('Medium.TButton',
                       font=('Arial', 10),
                       padding=(8, 6))

        # Style pour les labels de section
        style.configure('Section.TLabel',
                       font=('Arial', 11, 'bold'))

    def on_resolution_change(self, event=None):
        """Gestion du changement de résolution"""
        self.current_resolution = self.resolution_var.get()

    def apply_resolution(self):
        """Applique la résolution sélectionnée"""
        try:
            resolution = self.resolution_presets[self.current_resolution]
            self.root.geometry(resolution)
            self.status_var.set(f"✅ Résolution appliquée: {resolution}")
            print(f"Résolution changée: {resolution}")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur changement résolution: {e}")

    def on_template_type_change(self, event=None):
        """Gestion du changement de type de template"""
        self.current_template_type = self.template_type_var.get()
        self.update_save_info()

    def update_save_info(self):
        """Met à jour l'information de sauvegarde"""
        filename = f"{self.current_template_type}.png"
        filepath = os.path.join(self.templates_folder, filename)
        description = self.template_types.get(self.current_template_type, "Template personnalisé")

        self.save_info_var.set(f"💾 Sauvegarde: {filepath} ({description})")

    def add_custom_template_type(self):
        """Ajoute un nouveau type de template personnalisé"""
        from tkinter import simpledialog

        # Demander le nom du nouveau template
        template_name = simpledialog.askstring(
            "Nouveau Template",
            "Nom du nouveau type de template:\n(utilisé comme nom de fichier)",
            initialvalue="nouveau_template"
        )

        if template_name:
            # Nettoyer le nom (enlever espaces, caractères spéciaux)
            clean_name = "".join(c for c in template_name if c.isalnum() or c in "_-").lower()

            if clean_name and clean_name not in self.template_types:
                # Demander la description
                description = simpledialog.askstring(
                    "Description Template",
                    f"Description pour '{clean_name}':",
                    initialvalue=f"🎯 {template_name}"
                )

                if description:
                    # Ajouter le nouveau type
                    self.template_types[clean_name] = description

                    # Mettre à jour la combobox
                    template_combo = None
                    for widget in self.root.winfo_children():
                        if hasattr(widget, 'winfo_children'):
                            for child in widget.winfo_children():
                                if isinstance(child, ttk.Frame):
                                    for subchild in child.winfo_children():
                                        if isinstance(subchild, ttk.Combobox) and 'template' in str(subchild):
                                            template_combo = subchild
                                            break

                    # Mise à jour plus simple : recréer la liste
                    self.template_type_var.set(clean_name)
                    self.current_template_type = clean_name
                    self.update_save_info()

                    messagebox.showinfo("✅ Succès",
                                      f"Nouveau type de template ajouté:\n"
                                      f"• Nom: {clean_name}\n"
                                      f"• Description: {description}\n"
                                      f"• Fichier: {clean_name}.png")
                else:
                    messagebox.showwarning("Annulé", "Description requise")
            else:
                messagebox.showerror("Erreur",
                                   f"Nom invalide ou déjà existant: {clean_name}")

    def manage_templates(self):
        """Ouvre une fenêtre de gestion des templates"""
        manage_window = tk.Toplevel(self.root)
        manage_window.title("📋 Gestion des Templates")
        manage_window.geometry("800x600")

        # Configuration modale sécurisée
        try:
            manage_window.transient(self.root)
            manage_window.grab_set()
        except tk.TclError as e:
            print(f"Avertissement fenêtre modale: {e}")
            # Continuer sans modal si erreur

        # Frame principal
        main_frame = ttk.Frame(manage_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre
        title_label = ttk.Label(main_frame, text="📋 Templates Existants",
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 15))

        # Liste des templates avec scrollbar
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # Treeview pour afficher les templates
        columns = ('Nom', 'Description', 'Fichier', 'Taille', 'Modifié')
        tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # Configuration des colonnes
        tree.heading('Nom', text='Nom Template')
        tree.heading('Description', text='Description')
        tree.heading('Fichier', text='Fichier')
        tree.heading('Taille', text='Taille')
        tree.heading('Modifié', text='Dernière Modification')

        tree.column('Nom', width=150)
        tree.column('Description', width=200)
        tree.column('Fichier', width=180)
        tree.column('Taille', width=100)
        tree.column('Modifié', width=150)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Remplir la liste
        self.populate_templates_list(tree)

        # Boutons d'action
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        ttk.Button(buttons_frame, text="🔄 Actualiser",
                  command=lambda: self.populate_templates_list(tree)).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="👁️ Prévisualiser",
                  command=lambda: self.preview_selected_template(tree)).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="🗑️ Supprimer",
                  command=lambda: self.delete_selected_template(tree)).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="📁 Ouvrir Dossier",
                  command=self.open_templates_folder).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="✅ Fermer",
                  command=manage_window.destroy).pack(side=tk.RIGHT)

    def populate_templates_list(self, tree):
        """Remplit la liste des templates"""
        # Vider la liste
        for item in tree.get_children():
            tree.delete(item)

        # Scanner le dossier image
        if os.path.exists(self.templates_folder):
            for filename in os.listdir(self.templates_folder):
                if filename.endswith('.png') and not filename.endswith('_preview.png'):
                    filepath = os.path.join(self.templates_folder, filename)

                    # Informations du fichier
                    stat = os.stat(filepath)
                    size = f"{stat.st_size} bytes"
                    modified = time.strftime("%Y-%m-%d %H:%M", time.localtime(stat.st_mtime))

                    # Nom du template (sans extension)
                    template_name = filename[:-4]
                    description = self.template_types.get(template_name, "Template personnalisé")

                    # Ajouter à la liste
                    tree.insert('', 'end', values=(template_name, description, filename, size, modified))

    def preview_selected_template(self, tree):
        """Prévisualise le template sélectionné"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Sélectionnez un template à prévisualiser")
            return

        item = tree.item(selection[0])
        template_name = item['values'][0]
        filename = f"{template_name}.png"
        filepath = os.path.join(self.templates_folder, filename)

        if os.path.exists(filepath):
            # Ouvrir avec le visualiseur par défaut
            import subprocess
            try:
                subprocess.run(['start', filepath], shell=True, check=True)
            except:
                messagebox.showerror("Erreur", f"Impossible d'ouvrir {filepath}")
        else:
            messagebox.showerror("Erreur", f"Fichier non trouvé: {filepath}")

    def delete_selected_template(self, tree):
        """Supprime le template sélectionné"""
        selection = tree.selection()
        if not selection:
            messagebox.showwarning("Attention", "Sélectionnez un template à supprimer")
            return

        item = tree.item(selection[0])
        template_name = item['values'][0]

        if messagebox.askyesno("Confirmation",
                              f"Supprimer le template '{template_name}' ?\n"
                              f"Cette action est irréversible."):
            try:
                filename = f"{template_name}.png"
                filepath = os.path.join(self.templates_folder, filename)

                if os.path.exists(filepath):
                    os.remove(filepath)

                # Supprimer aussi la prévisualisation
                preview_path = os.path.join(self.templates_folder, f"{template_name}_preview.png")
                if os.path.exists(preview_path):
                    os.remove(preview_path)

                # Actualiser la liste
                self.populate_templates_list(tree)
                messagebox.showinfo("✅ Succès", f"Template '{template_name}' supprimé")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur suppression: {e}")

    def open_templates_folder(self):
        """Ouvre le dossier des templates"""
        import subprocess
        try:
            subprocess.run(['explorer', os.path.abspath(self.templates_folder)], check=True)
        except Exception as e:
            messagebox.showerror("Erreur", f"Impossible d'ouvrir le dossier: {e}")

    def setup_ui(self):
        """Interface utilisateur améliorée"""

        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Titre avec émoji (police agrandie)
        title_label = ttk.Label(main_frame, text="🎯 Sélecteur d'icône recherche Clarify",
                               font=("Arial", 20, "bold"))
        title_label.pack(pady=(0, 15))

        # Instructions améliorées (police agrandie)
        instructions = ttk.Label(main_frame,
                                text="📋 Instructions:\n"
                                     "1. Sélectionnez une fenêtre Clarify dans la liste\n"
                                     "2. Cliquez sur '📷 Capturer' pour prendre une capture d'écran\n"
                                     "3. Cliquez sur l'icône de recherche (🔍) dans l'image\n"
                                     "4. Ajustez la taille du template si nécessaire\n"
                                     "5. Cliquez sur '✅ Créer Template' pour sauvegarder",
                                justify=tk.LEFT, font=("Arial", 12))
        instructions.pack(pady=(0, 20))

        # Section configuration
        config_frame = ttk.LabelFrame(main_frame, text="⚙️ Configuration", padding="10")
        config_frame.pack(fill=tk.X, pady=(0, 15))

        # Première ligne : Résolution et Type de template
        config_row1 = ttk.Frame(config_frame)
        config_row1.pack(fill=tk.X, pady=(0, 10))

        # Sélection de résolution
        resolution_frame = ttk.Frame(config_row1)
        resolution_frame.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(resolution_frame, text="📐 Résolution:", font=('Arial', 11)).pack(side=tk.LEFT, padx=(0, 8))
        self.resolution_var = tk.StringVar(value=self.current_resolution)
        resolution_combo = ttk.Combobox(resolution_frame, textvariable=self.resolution_var,
                                       values=list(self.resolution_presets.keys()),
                                       state="readonly", width=15, font=('Arial', 10))
        resolution_combo.pack(side=tk.LEFT, padx=(0, 10))
        resolution_combo.bind('<<ComboboxSelected>>', self.on_resolution_change)

        # Bouton appliquer résolution
        apply_res_btn = ttk.Button(resolution_frame, text="✅ Appliquer",
                                  command=self.apply_resolution,
                                  style='Medium.TButton')
        apply_res_btn.pack(side=tk.LEFT)

        # Sélection du type de template
        template_frame = ttk.Frame(config_row1)
        template_frame.pack(side=tk.LEFT)

        ttk.Label(template_frame, text="🎯 Type Template:", font=('Arial', 11)).pack(side=tk.LEFT, padx=(0, 8))
        self.template_type_var = tk.StringVar(value=self.current_template_type)
        template_combo = ttk.Combobox(template_frame, textvariable=self.template_type_var,
                                     values=list(self.template_types.keys()),
                                     state="readonly", width=20, font=('Arial', 10))
        template_combo.pack(side=tk.LEFT, padx=(0, 10))
        template_combo.bind('<<ComboboxSelected>>', self.on_template_type_change)

        # Bouton pour ajouter un nouveau type de template
        add_template_btn = ttk.Button(template_frame, text="➕ Nouveau",
                                     command=self.add_custom_template_type,
                                     style='Medium.TButton')
        add_template_btn.pack(side=tk.LEFT)

        # Affichage du nom de fichier de sauvegarde
        self.save_info_var = tk.StringVar()
        self.update_save_info()
        save_info_label = ttk.Label(config_frame, textvariable=self.save_info_var,
                                   font=('Arial', 10, 'italic'), foreground='blue')
        save_info_label.pack(pady=(5, 0))

        # Section sélection de fenêtre Clarify
        clarify_frame = ttk.LabelFrame(main_frame, text="🖥️ Fenêtres Clarify détectées", padding="5")
        clarify_frame.pack(fill=tk.X, pady=(0, 10))

        clarify_control_frame = ttk.Frame(clarify_frame)
        clarify_control_frame.pack(fill=tk.X)

        self.clarify_var = tk.StringVar()
        self.clarify_combo = ttk.Combobox(clarify_control_frame, textvariable=self.clarify_var,
                                         state="readonly", width=70, font=('Arial', 11))
        self.clarify_combo.pack(side=tk.LEFT, padx=(0, 15))

        refresh_btn = ttk.Button(clarify_control_frame, text="🔄 Actualiser",
                                command=self.refresh_clarify_windows,
                                style='Medium.TButton')
        refresh_btn.pack(side=tk.LEFT)

        # Boutons de contrôle principaux
        control_frame = ttk.LabelFrame(main_frame, text="🎮 Contrôles", padding="5")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack()

        self.capture_btn = ttk.Button(buttons_frame, text="📷 Capturer Clarify",
                                     command=self.capture_clarify,
                                     style='Large.TButton')
        self.capture_btn.pack(side=tk.LEFT, padx=(0, 15))

        # Bouton capture avec délai
        self.capture_delay_btn = ttk.Button(buttons_frame, text="⏱️ Capturer avec délai (5s)",
                                           command=self.capture_clarify_with_delay,
                                           style='Medium.TButton')
        self.capture_delay_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Option pour forcer Clarify au premier plan
        self.force_foreground_var = tk.BooleanVar(value=True)
        force_fg_check = ttk.Checkbutton(buttons_frame,
                                        text="🔒 Forcer Clarify au premier plan",
                                        variable=self.force_foreground_var)
        force_fg_check.pack(side=tk.LEFT, padx=(15, 0))

        # Taille du template
        size_frame = ttk.Frame(buttons_frame)
        size_frame.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(size_frame, text="📏 Taille:", font=('Arial', 11)).pack(side=tk.LEFT)
        self.size_var = tk.StringVar(value="20")
        size_spin = ttk.Spinbox(size_frame, from_=12, to=50, width=6,
                               textvariable=self.size_var,
                               command=self.on_size_change,
                               font=('Arial', 11))
        size_spin.pack(side=tk.LEFT, padx=(8, 0))

        self.create_btn = ttk.Button(buttons_frame, text="✅ Créer Template",
                                    command=self.create_template, state=tk.DISABLED,
                                    style='Large.TButton')
        self.create_btn.pack(side=tk.LEFT, padx=(15, 0))

        # Boutons supplémentaires
        extra_buttons_frame = ttk.Frame(control_frame)
        extra_buttons_frame.pack(pady=(5, 0))

        load_btn = ttk.Button(extra_buttons_frame, text="📁 Charger Image",
                             command=self.load_external_image,
                             style='Medium.TButton')
        load_btn.pack(side=tk.LEFT, padx=(0, 12))

        save_btn = ttk.Button(extra_buttons_frame, text="💾 Sauver Template",
                             command=self.save_template_as, state=tk.DISABLED,
                             style='Medium.TButton')
        save_btn.pack(side=tk.LEFT, padx=(0, 12))
        self.save_btn = save_btn

        test_btn = ttk.Button(extra_buttons_frame, text="🧪 Tester Template",
                             command=self.test_current_template, state=tk.DISABLED,
                             style='Medium.TButton')
        test_btn.pack(side=tk.LEFT, padx=(0, 12))
        self.test_btn = test_btn

        # Bouton pour gérer les templates existants
        manage_btn = ttk.Button(extra_buttons_frame, text="📋 Gérer Templates",
                               command=self.manage_templates,
                               style='Medium.TButton')
        manage_btn.pack(side=tk.LEFT)

        # Statut avec couleurs (police agrandie)
        self.status_var = tk.StringVar(value="⏸️ Prêt - Sélectionnez une fenêtre Clarify et cliquez 'Capturer'")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var,
                                     font=("Arial", 11, "bold"))
        self.status_label.pack(pady=(0, 15))
        
        # Canvas pour l'image
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # Canvas avec scrollbars
        self.canvas = tk.Canvas(canvas_frame, bg="white", cursor="crosshair")
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Layout scrollbars
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Bind événements
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<Motion>", self.on_canvas_motion)
        
        # Variables pour l'image
        self.image_on_canvas = None
        self.photo_image = None
        self.current_template = None

    def refresh_clarify_windows(self):
        """Actualise la liste des fenêtres Clarify"""
        self.clarify_windows = self.find_clarify_window()

        # Mettre à jour la combobox
        if self.clarify_windows:
            window_list = [f"{title} (ID: {hwnd})" for hwnd, title in self.clarify_windows]
            self.clarify_combo['values'] = window_list
            self.clarify_combo.current(0)  # Sélectionner la première
            self.status_var.set(f"✅ {len(self.clarify_windows)} fenêtre(s) Clarify trouvée(s)")
        else:
            self.clarify_combo['values'] = ["Aucune fenêtre Clarify trouvée"]
            self.clarify_combo.current(0)
            self.status_var.set("❌ Aucune fenêtre Clarify trouvée - Ouvrez Clarify d'abord")

    def find_clarify_window(self):
        """Trouve toutes les fenêtres Clarify"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                # Recherche plus large pour différentes versions de Clarify
                clarify_keywords = [
                    "Amdocs CRM - ClearSupport",
                    "CLARIFY",
                    "Clarify",
                    "ClearSupport",
                    "Amdocs CRM"
                ]

                for keyword in clarify_keywords:
                    if keyword in window_text:
                        windows.append((hwnd, window_text))
                        break
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        return windows

    def get_selected_clarify_window(self):
        """Récupère la fenêtre Clarify sélectionnée"""
        if not self.clarify_windows:
            return None

        selected_text = self.clarify_var.get()
        if "ID:" not in selected_text:
            return None

        # Extraire l'ID de la fenêtre
        try:
            hwnd_str = selected_text.split("ID: ")[1].rstrip(")")
            hwnd = int(hwnd_str)

            # Trouver la fenêtre correspondante
            for window_hwnd, window_title in self.clarify_windows:
                if window_hwnd == hwnd:
                    return window_hwnd, window_title
        except:
            pass

        return None

    def on_size_change(self):
        """Appelé quand la taille du template change"""
        if self.selected_coords and self.screenshot_path:
            # Redessiner le marqueur avec la nouvelle taille
            canvas_coords = self.real_to_canvas_coords(*self.selected_coords)
            if canvas_coords:
                self.show_selection_marker(*canvas_coords)

    def load_external_image(self):
        """Charge une image externe"""
        file_path = filedialog.askopenfilename(
            title="Sélectionner une image",
            filetypes=[
                ("Images", "*.png *.jpg *.jpeg *.bmp *.gif"),
                ("PNG", "*.png"),
                ("JPEG", "*.jpg *.jpeg"),
                ("Tous les fichiers", "*.*")
            ]
        )

        if file_path:
            self.screenshot_path = file_path
            self.load_image(file_path)
            self.status_var.set(f"📁 Image chargée: {os.path.basename(file_path)}")

    def save_template_as(self):
        """Sauvegarde le template avec un nom personnalisé"""
        if not self.current_template:
            messagebox.showerror("Erreur", "Aucun template créé")
            return

        file_path = filedialog.asksaveasfilename(
            title="Sauvegarder le template",
            defaultextension=".png",
            filetypes=[("PNG", "*.png"), ("Tous les fichiers", "*.*")],
            initialname="mon_template.png"
        )

        if file_path:
            cv2.imwrite(file_path, self.current_template)
            messagebox.showinfo("Succès", f"Template sauvé: {os.path.basename(file_path)}")

    def test_current_template(self):
        """Teste le template actuel"""
        if not self.current_template or not self.screenshot_path:
            messagebox.showerror("Erreur", "Template ou image manquant")
            return

        # Sauver temporairement le template
        temp_template_path = "temp_template_test.png"
        cv2.imwrite(temp_template_path, self.current_template)

        # Tester
        self.test_template(self.screenshot_path, temp_template_path)

        # Nettoyer
        try:
            os.remove(temp_template_path)
        except:
            pass
    
    def capture_clarify_win32(self, hwnd):
        """Capture avec Win32 - Version améliorée pour gérer les changements d'état"""
        try:
            self.status_var.set("Capture en cours...")
            self.root.update()

            # Sauvegarder l'état actuel de la fenêtre
            was_visible = self.root.winfo_viewable()

            # Cacher la fenêtre temporairement (plus sûr que iconify pour les modales)
            self.root.withdraw()
            time.sleep(0.3)

            # Vérifier que la fenêtre Clarify existe encore
            if not win32gui.IsWindow(hwnd):
                messagebox.showerror("Erreur", "La fenêtre Clarify n'est plus disponible")
                if was_visible:
                    self.root.deiconify()
                return None

            # Activer Clarify et attendre qu'elle soit stable
            if self.force_foreground_var.get():
                # Forcer Clarify au premier plan avec plusieurs méthodes
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)  # S'assurer qu'elle n'est pas minimisée
                win32gui.SetForegroundWindow(hwnd)
                win32gui.BringWindowToTop(hwnd)

                # Méthode alternative pour forcer l'activation
                try:
                    # Simuler un clic Alt pour activer la fenêtre
                    windll.user32.keybd_event(0x12, 0, 0, 0)  # Alt down
                    windll.user32.keybd_event(0x12, 0, 2, 0)  # Alt up
                    win32gui.SetForegroundWindow(hwnd)
                except:
                    pass

                time.sleep(1.0)  # Temps d'attente plus long pour stabilisation

                # Vérifier que la fenêtre est bien au premier plan
                foreground_hwnd = win32gui.GetForegroundWindow()
                if foreground_hwnd != hwnd:
                    # Dernière tentative avec SetWindowPos
                    try:
                        win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST, 0, 0, 0, 0,
                                            win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
                        time.sleep(0.2)
                        win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, 0, 0, 0, 0,
                                            win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
                    except:
                        pass
            else:
                # Activation simple
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)

            # Capture avec plusieurs tentatives si nécessaire
            screenshot_path = None
            max_attempts = 3

            for attempt in range(max_attempts):
                try:
                    # Capture
                    hwndDC = win32gui.GetWindowDC(hwnd)
                    mfcDC = win32ui.CreateDCFromHandle(hwndDC)
                    saveDC = mfcDC.CreateCompatibleDC()

                    rect = win32gui.GetWindowRect(hwnd)
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]

                    if width <= 0 or height <= 0:
                        raise Exception("Dimensions de fenêtre invalides")

                    saveBitMap = win32ui.CreateBitmap()
                    saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
                    saveDC.SelectObject(saveBitMap)

                    # Essayer différentes méthodes de capture
                    result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 3)

                    if not result:
                        # Méthode alternative
                        result = windll.user32.PrintWindow(hwnd, saveDC.GetSafeHdc(), 0)

                    if result:
                        bmpinfo = saveBitMap.GetInfo()
                        bmpstr = saveBitMap.GetBitmapBits(True)
                        img = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                                             bmpstr, 'raw', 'BGRX', 0, 1)

                        # Vérifier que l'image n'est pas complètement noire (échec de capture)
                        img_array = np.array(img)
                        if np.mean(img_array) > 10:  # Image valide
                            # Sauver avec timestamp pour éviter les conflits
                            timestamp = int(time.time())
                            screenshot_path = f"clarify_selector_{timestamp}.png"
                            img.save(screenshot_path)

                            # Nettoyage
                            win32gui.DeleteObject(saveBitMap.GetHandle())
                            saveDC.DeleteDC()
                            mfcDC.DeleteDC()
                            win32gui.ReleaseDC(hwnd, hwndDC)

                            break  # Sortir de la boucle, capture réussie
                        else:
                            print(f"Tentative {attempt + 1}: Image trop sombre, nouvelle tentative...")

                    # Nettoyage pour cette tentative
                    win32gui.DeleteObject(saveBitMap.GetHandle())
                    saveDC.DeleteDC()
                    mfcDC.DeleteDC()
                    win32gui.ReleaseDC(hwnd, hwndDC)

                    if attempt < max_attempts - 1:
                        time.sleep(0.5)  # Attendre avant la prochaine tentative

                except Exception as e:
                    print(f"Erreur tentative {attempt + 1}: {e}")
                    if attempt < max_attempts - 1:
                        time.sleep(0.5)
                    continue

            # Restaurer cette fenêtre
            if was_visible:
                self.root.deiconify()
                self.root.lift()
                self.root.focus_force()

            return screenshot_path

        except Exception as e:
            # Toujours restaurer la fenêtre en cas d'erreur
            try:
                if was_visible:
                    self.root.deiconify()
            except:
                pass
            messagebox.showerror("Erreur", f"Erreur capture: {e}")
            return None
    
    def capture_clarify(self):
        """Capture l'écran de la fenêtre Clarify sélectionnée"""
        selected_window = self.get_selected_clarify_window()

        if not selected_window:
            messagebox.showerror("Erreur",
                               "Aucune fenêtre Clarify sélectionnée.\n"
                               "Sélectionnez une fenêtre dans la liste ou cliquez 'Actualiser'")
            return

        hwnd, title = selected_window
        self.status_var.set(f"🔄 Capture en cours: {title}")
        self.root.update()

        screenshot_path = self.capture_clarify_win32(hwnd)

        if screenshot_path:
            self.screenshot_path = screenshot_path
            self.load_image(screenshot_path)
            self.status_var.set("✅ Capture réussie - Cliquez sur l'icône recherche (🔍) dans l'image")

            # Réinitialiser la sélection
            self.selected_coords = None
            self.current_template = None
            self.create_btn.config(state=tk.DISABLED)
            self.save_btn.config(state=tk.DISABLED)
            self.test_btn.config(state=tk.DISABLED)
        else:
            self.status_var.set("❌ Échec de la capture - Vérifiez que Clarify est visible")

    def capture_clarify_with_delay(self):
        """Capture Clarify après un délai de 5 secondes pour permettre les interactions"""
        selected_window = self.get_selected_clarify_window()

        if not selected_window:
            messagebox.showerror("Erreur",
                               "Aucune fenêtre Clarify sélectionnée.\n"
                               "Sélectionnez une fenêtre dans la liste ou cliquez 'Actualiser'")
            return

        hwnd, title = selected_window

        # Afficher un dialogue de compte à rebours
        countdown_window = tk.Toplevel(self.root)
        countdown_window.title("⏱️ Capture avec délai")
        countdown_window.geometry("400x200")
        countdown_window.resizable(False, False)
        countdown_window.transient(self.root)
        countdown_window.grab_set()

        # Centrer la fenêtre
        countdown_window.update_idletasks()
        x = (countdown_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (countdown_window.winfo_screenheight() // 2) - (200 // 2)
        countdown_window.geometry(f"400x200+{x}+{y}")

        # Interface du compte à rebours
        main_frame = ttk.Frame(countdown_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        title_label = ttk.Label(main_frame, text="🎯 Capture programmée",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))

        instruction_label = ttk.Label(main_frame,
                                     text="Vous avez 5 secondes pour :\n"
                                          "• Cliquer sur des boutons dans Clarify\n"
                                          "• Ouvrir des menus\n"
                                          "• Naviguer vers l'écran souhaité\n\n"
                                          "La capture se fera automatiquement !",
                                     justify=tk.CENTER, font=("Arial", 11))
        instruction_label.pack(pady=(0, 15))

        countdown_var = tk.StringVar()
        countdown_label = ttk.Label(main_frame, textvariable=countdown_var,
                                   font=("Arial", 24, "bold"), foreground="red")
        countdown_label.pack(pady=(0, 15))

        # Bouton d'annulation
        cancel_btn = ttk.Button(main_frame, text="❌ Annuler",
                               command=countdown_window.destroy)
        cancel_btn.pack()

        # Variable pour contrôler l'annulation
        self.capture_cancelled = False

        def on_cancel():
            self.capture_cancelled = True
            countdown_window.destroy()

        cancel_btn.config(command=on_cancel)
        countdown_window.protocol("WM_DELETE_WINDOW", on_cancel)

        # Fonction de compte à rebours
        def countdown(seconds):
            if self.capture_cancelled:
                return

            if seconds > 0:
                countdown_var.set(f"⏱️ {seconds}")
                countdown_window.after(1000, lambda: countdown(seconds - 1))
            else:
                countdown_var.set("📷 CAPTURE !")
                countdown_window.after(500, perform_capture)

        def perform_capture():
            if self.capture_cancelled:
                return

            countdown_window.destroy()

            # Effectuer la capture
            self.status_var.set(f"🔄 Capture en cours: {title}")
            self.root.update()

            screenshot_path = self.capture_clarify_win32(hwnd)

            if screenshot_path:
                self.screenshot_path = screenshot_path
                self.load_image(screenshot_path)
                self.status_var.set("✅ Capture avec délai réussie - Cliquez sur l'icône recherche (🔍)")

                # Réinitialiser la sélection
                self.selected_coords = None
                self.current_template = None
                self.create_btn.config(state=tk.DISABLED)
                self.save_btn.config(state=tk.DISABLED)
                self.test_btn.config(state=tk.DISABLED)
            else:
                self.status_var.set("❌ Échec de la capture avec délai")

        # Démarrer le compte à rebours
        countdown(5)

    def auto_refresh_windows(self):
        """Rafraîchit automatiquement la liste des fenêtres Clarify toutes les 3 secondes"""
        try:
            old_count = len(self.clarify_windows)
            self.refresh_clarify_windows()
            new_count = len(self.clarify_windows)

            # Notifier si le nombre de fenêtres a changé
            if new_count != old_count:
                if new_count > old_count:
                    self.status_var.set(f"✅ Nouvelle fenêtre Clarify détectée ({new_count} total)")
                elif new_count < old_count:
                    self.status_var.set(f"⚠️ Fenêtre Clarify fermée ({new_count} restante(s))")
        except Exception as e:
            print(f"Erreur auto-refresh: {e}")

        # Programmer le prochain rafraîchissement
        self.root.after(3000, self.auto_refresh_windows)
    
    def load_image(self, image_path):
        """Charge l'image dans le canvas"""
        try:
            # Charger l'image
            img = Image.open(image_path)
            
            # Redimensionner si trop grande
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width <= 1:  # Canvas pas encore initialisé
                canvas_width = 1200  # Taille par défaut agrandie
                canvas_height = 800

            img_width, img_height = img.size

            # Calculer le facteur de redimensionnement (plus permissif)
            # Permettre des images jusqu'à 150% de la taille du canvas
            max_width = canvas_width * 1.5
            max_height = canvas_height * 1.5

            scale_x = max_width / img_width
            scale_y = max_height / img_height
            scale = min(scale_x, scale_y, 2.0)  # Permettre un agrandissement jusqu'à 2x

            # Redimensionner seulement si l'image est vraiment trop grande ou trop petite
            if scale < 0.8 or (scale > 1.2 and img_width < 800):
                new_width = int(img_width * scale)
                new_height = int(img_height * scale)
                img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Convertir pour Tkinter
            self.photo_image = ImageTk.PhotoImage(img)
            
            # Effacer le canvas
            self.canvas.delete("all")
            
            # Ajouter l'image
            self.image_on_canvas = self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)
            
            # Configurer la zone de scroll
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur chargement image: {e}")
    
    def real_to_canvas_coords(self, real_x, real_y):
        """Convertit les coordonnées réelles en coordonnées canvas"""
        if not self.photo_image or not self.screenshot_path:
            return None

        try:
            original_img = Image.open(self.screenshot_path)
            original_width, original_height = original_img.size
            display_width = self.photo_image.width()
            display_height = self.photo_image.height()

            scale_x = display_width / original_width
            scale_y = display_height / original_height

            canvas_x = real_x * scale_x
            canvas_y = real_y * scale_y

            return canvas_x, canvas_y
        except:
            return None

    def on_canvas_click(self, event):
        """Gestion du clic sur le canvas"""
        if not self.screenshot_path:
            messagebox.showwarning("Attention", "Capturez d'abord une image")
            return

        # Coordonnées dans le canvas
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)

        # Convertir en coordonnées image réelles
        if self.photo_image:
            try:
                # Facteur de redimensionnement
                original_img = Image.open(self.screenshot_path)
                original_width, original_height = original_img.size
                display_width = self.photo_image.width()
                display_height = self.photo_image.height()

                scale_x = original_width / display_width
                scale_y = original_height / display_height

                # Coordonnées réelles
                real_x = int(canvas_x * scale_x)
                real_y = int(canvas_y * scale_y)

                # Vérifier que les coordonnées sont dans l'image
                if 0 <= real_x < original_width and 0 <= real_y < original_height:
                    self.selected_coords = (real_x, real_y)

                    # Marquer la sélection sur le canvas
                    self.show_selection_marker(canvas_x, canvas_y)

                    # Activer les boutons
                    self.create_btn.config(state=tk.NORMAL)

                    # Mettre à jour le statut
                    size = int(self.size_var.get())
                    self.status_var.set(f"🎯 Sélectionné: ({real_x}, {real_y}) - Template {size}x{size} - Cliquez 'Créer Template'")
                else:
                    messagebox.showwarning("Attention", "Cliquez à l'intérieur de l'image")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sélection: {e}")
    
    def show_selection_marker(self, x, y):
        """Affiche un marqueur de sélection amélioré"""
        # Effacer les anciens marqueurs
        self.canvas.delete("selection")

        # Taille du template pour le marqueur
        template_size = int(self.size_var.get())

        # Calculer la taille du marqueur en coordonnées canvas
        if self.photo_image and self.screenshot_path:
            try:
                original_img = Image.open(self.screenshot_path)
                original_width, original_height = original_img.size
                display_width = self.photo_image.width()
                display_height = self.photo_image.height()

                scale_x = display_width / original_width
                scale_y = display_height / original_height

                marker_width = template_size * scale_x
                marker_height = template_size * scale_y
            except:
                marker_width = marker_height = template_size
        else:
            marker_width = marker_height = template_size

        # Rectangle de sélection (zone du template)
        half_w = marker_width / 2
        half_h = marker_height / 2
        self.canvas.create_rectangle(x - half_w, y - half_h, x + half_w, y + half_h,
                                   outline="lime", width=2, tags="selection")

        # Croix de visée
        cross_size = max(15, marker_width / 2)
        self.canvas.create_line(x - cross_size, y, x + cross_size, y,
                               fill="red", width=2, tags="selection")
        self.canvas.create_line(x, y - cross_size, x, y + cross_size,
                               fill="red", width=2, tags="selection")

        # Point central
        self.canvas.create_oval(x-2, y-2, x+2, y+2, fill="red", outline="red", tags="selection")

        # Texte informatif
        if self.selected_coords:
            real_x, real_y = self.selected_coords
            text = f"🎯 ({real_x},{real_y})\n📏 {template_size}x{template_size}"
            self.canvas.create_text(x, y - cross_size - 25, text=text, fill="red",
                                   font=("Arial", 12, "bold"), tags="selection", justify=tk.CENTER)
    
    def on_canvas_motion(self, event):
        """Affiche les coordonnées au survol"""
        if not self.screenshot_path:
            return
        
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        # Afficher dans la barre de titre temporairement
        self.root.title(f"Sélecteur d'icône - Position: ({int(canvas_x)}, {int(canvas_y)})")
    
    def create_template(self):
        """Crée le template à partir de la sélection"""
        if not self.selected_coords or not self.screenshot_path:
            messagebox.showerror("Erreur", "Aucune sélection faite")
            return

        try:
            x, y = self.selected_coords
            size = int(self.size_var.get())

            self.status_var.set("🔄 Création du template en cours...")
            self.root.update()

            # Charger l'image originale
            img = cv2.imread(self.screenshot_path)
            if img is None:
                messagebox.showerror("Erreur", "Impossible de charger l'image")
                return

            height, width = img.shape[:2]

            # Calculer les bordures avec vérification
            half_size = size // 2
            x1 = max(0, x - half_size)
            y1 = max(0, y - half_size)
            x2 = min(width, x + half_size)
            y2 = min(height, y + half_size)

            # Vérifier que le template a une taille valide
            if x2 <= x1 or y2 <= y1:
                messagebox.showerror("Erreur", "Zone de template invalide")
                return

            # Extraire le template
            template = img[y1:y2, x1:x2]
            self.current_template = template.copy()

            # Sauvegarder le template avec le nom approprié
            template_filename = f"{self.current_template_type}.png"
            template_path = os.path.join(self.templates_folder, template_filename)
            cv2.imwrite(template_path, template)

            # Sauvegarder aussi une copie avec timestamp pour historique
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{self.current_template_type}_{timestamp}.png"
            backup_path = os.path.join(self.templates_folder, "backup", backup_filename)

            # Créer le dossier backup s'il n'existe pas
            backup_folder = os.path.join(self.templates_folder, "backup")
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)
            cv2.imwrite(backup_path, template)

            # Créer une image de prévisualisation détaillée
            preview = img.copy()

            # Rectangle de sélection
            cv2.rectangle(preview, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # Point central
            cv2.circle(preview, (x, y), 3, (0, 0, 255), -1)

            # Informations textuelles
            cv2.putText(preview, f"Template {size}x{size}", (x1, y1-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.putText(preview, f"Position: ({x},{y})", (x1, y2+20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)

            # Sauvegarder la prévisualisation dans le dossier image
            preview_filename = f"{self.current_template_type}_preview.png"
            preview_path = os.path.join(self.templates_folder, preview_filename)
            cv2.imwrite(preview_path, preview)

            # Tester automatiquement le template
            test_score = self.test_template(self.screenshot_path, template_path)

            # Activer les boutons
            self.save_btn.config(state=tk.NORMAL)
            self.test_btn.config(state=tk.NORMAL)

            # Message de succès avec score
            score_text = f" (Score: {test_score:.3f})" if test_score else ""
            template_description = self.template_types.get(self.current_template_type, "Template personnalisé")

            messagebox.showinfo("✅ Succès",
                              f"Template '{template_description}' créé avec succès!{score_text}\n\n"
                              f"📁 Fichiers créés:\n"
                              f"• {template_path}\n"
                              f"• {preview_path}\n"
                              f"• {backup_path} (sauvegarde)\n"
                              f"• test_result.png (si test réussi)\n\n"
                              f"📏 Taille: {template.shape[1]}x{template.shape[0]} pixels\n"
                              f"🎯 Type: {self.current_template_type}")

            self.status_var.set(f"✅ Template créé! Taille: {template.shape[1]}x{template.shape[0]}{score_text}")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur création template: {e}")
            self.status_var.set("❌ Erreur lors de la création du template")
    
    def test_template(self, screenshot_path, template_path):
        """Teste le template créé avec retour du score"""
        try:
            screenshot = cv2.imread(screenshot_path)
            template = cv2.imread(template_path)

            if screenshot is None or template is None:
                print("❌ Impossible de charger les images pour le test")
                return None

            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)

            # Template matching avec différentes méthodes
            methods = [
                (cv2.TM_CCOEFF_NORMED, "CCOEFF_NORMED"),
                (cv2.TM_CCORR_NORMED, "CCORR_NORMED"),
                (cv2.TM_SQDIFF_NORMED, "SQDIFF_NORMED")
            ]

            best_score = 0
            best_method = None
            best_location = None

            for method, method_name in methods:
                result = cv2.matchTemplate(screenshot_gray, template_gray, method)
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                # Pour SQDIFF, plus petit = meilleur
                if method == cv2.TM_SQDIFF_NORMED:
                    score = 1 - min_val
                    location = min_loc
                else:
                    score = max_val
                    location = max_loc

                if score > best_score:
                    best_score = score
                    best_method = method_name
                    best_location = location

            # Seuil de confiance
            confidence_threshold = 0.7

            if best_score > confidence_threshold:
                # Marquer le résultat
                template_h, template_w = template_gray.shape
                center_x = best_location[0] + template_w // 2
                center_y = best_location[1] + template_h // 2

                result_img = screenshot.copy()

                # Rectangle de détection
                cv2.rectangle(result_img, best_location,
                             (best_location[0] + template_w, best_location[1] + template_h),
                             (0, 255, 0), 3)

                # Point central
                cv2.circle(result_img, (center_x, center_y), 5, (0, 0, 255), -1)

                # Informations
                cv2.putText(result_img, f"Score: {best_score:.3f} ({best_method})",
                           (best_location[0], best_location[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                cv2.putText(result_img, f"Position: ({center_x}, {center_y})",
                           (best_location[0], best_location[1] + template_h + 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)

                cv2.imwrite("test_result.png", result_img)

                print(f"✅ Test réussi! Score: {best_score:.3f} ({best_method})")
                print(f"   Position détectée: ({center_x}, {center_y})")
                return best_score
            else:
                print(f"⚠️ Test faible. Meilleur score: {best_score:.3f} ({best_method})")
                print(f"   Seuil requis: {confidence_threshold}")

                # Créer quand même une image de résultat pour diagnostic
                if best_location:
                    template_h, template_w = template_gray.shape
                    result_img = screenshot.copy()
                    cv2.rectangle(result_img, best_location,
                                 (best_location[0] + template_w, best_location[1] + template_h),
                                 (0, 0, 255), 2)  # Rouge pour faible score
                    cv2.putText(result_img, f"Faible: {best_score:.3f}",
                               (best_location[0], best_location[1] - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                    cv2.imwrite("test_result_low_score.png", result_img)

                return best_score

        except Exception as e:
            print(f"❌ Erreur test: {e}")
            return None
    
    def run(self):
        """Lance l'interface"""
        self.root.mainloop()

def main():
    print("=== SÉLECTEUR VISUEL D'ICÔNE RECHERCHE ===")
    print("Lancement de l'interface graphique...")
    
    try:
        selector = IconSelector()
        selector.run()
    except Exception as e:
        print(f"Erreur: {e}")

if __name__ == "__main__":
    # Vérifier les dépendances
    try:
        import cv2
    except ImportError:
        print("Installez: pip install opencv-python")
        exit(1)
    
    main()