# ========================================
# CONFIGURATION SIMPLE CLARIFY AUTOMATION
# ========================================
# 
# Ce fichier permet de configurer facilement les étapes de l'automatisation Clarify
# Modifiez les valeurs selon vos besoins, puis utilisez le convertisseur pour générer le JSON
#

# === FICHIERS ===
FICHIER_EXCEL_IDS = IDs_a_traiter.xlsx
FICHIER_SUIVI = Clarify_Suivi_Traitement.xlsx

# === IMAGES TEMPLATES ===
IMAGE_ICONE_RECHERCHE = image/search_icon_selected.png
IMAGE_OK_01 = image/OK_01.png
IMAGE_SUIVI_RESOLUTION = image/suivi_resolution.png
IMAGE_BUREAU = image/bureau.png
IMAGE_REPRENDRE = image/reprendre.png
IMAGE_ACCEPTER_REPRENDRE = image/accept_reprendre.png

# === DÉLAIS (en secondes) ===
DELAI_APRES_CLIC_RECHERCHE = 3.0
DELAI_APRES_SAISIE_ID = 0.5
DELAI_APRES_LANCEMENT_RECHERCHE = 4.0
DELAI_APRES_CAS_REUSSI = 2.0
DELAI_ENTRE_CAS = 2.0
DELAI_CTRL_A = 0.2
DELAI_APRES_CLIC = 1.0

# === SEUILS DE DÉTECTION D'IMAGES ===
# Plus la valeur est élevée, plus la détection est stricte
# Valeurs recommandées : entre 0.4 (souple) et 0.8 (strict)
SEUIL_DETECTION_PRINCIPAL = 0.6
SEUIL_DETECTION_RAPIDE = 0.5
SEUILS_MULTIPLES = 0.7,0.6,0.5,0.4

# === ÉTAPES DU PROCESSUS ===
# Mettez OUI pour activer, NON pour désactiver

# Étape 1 : Clic sur l'icône de recherche
ETAPE_CLIC_RECHERCHE_ACTIVE = OUI
ETAPE_CLIC_RECHERCHE_OBLIGATOIRE = OUI
ETAPE_CLIC_RECHERCHE_TENTATIVES = 3

# Étape 2 : Attente ouverture fenêtre
ETAPE_ATTENTE_FENETRE_ACTIVE = OUI
ETAPE_ATTENTE_FENETRE_DUREE = 3.0

# Étape 3 : Saisie de l'ID
ETAPE_SAISIE_ID_ACTIVE = OUI
ETAPE_SAISIE_ID_UTILISER_CTRL_A = OUI
ETAPE_SAISIE_ID_VIDER_CHAMP = OUI

# Étape 4 : Lancement recherche (Entrée)
ETAPE_LANCEMENT_RECHERCHE_ACTIVE = OUI
ETAPE_LANCEMENT_RECHERCHE_TOUCHE = enter

# Étape 5 : Attente des résultats
ETAPE_ATTENTE_RESULTATS_ACTIVE = OUI
ETAPE_ATTENTE_RESULTATS_DUREE = 4.0

# Étape 6 : Clic sur OK_01 (avant Suivi Résolution)
ETAPE_CLIC_OK_01_ACTIVE = OUI
ETAPE_CLIC_OK_01_OBLIGATOIRE = OUI
ETAPE_CLIC_OK_01_TENTATIVES = 3
ETAPE_CLIC_OK_01_ATTENTE_APRES = 10.0

# Étape 7 : Clic sur "Suivi Résolution"
ETAPE_SUIVI_RESOLUTION_ACTIVE = OUI
ETAPE_SUIVI_RESOLUTION_OBLIGATOIRE = OUI
ETAPE_SUIVI_RESOLUTION_TENTATIVES = 2

# Étape 8 : Clics additionnels (bureau, reprendre, accepter)
ETAPE_CLICS_ADDITIONNELS_ACTIVE = NON
ETAPE_CLIC_BUREAU_ACTIVE = NON
ETAPE_CLIC_REPRENDRE_ACTIVE = NON
ETAPE_CLIC_ACCEPTER_ACTIVE = NON

# === GESTION D'ERREURS ===
CONTINUER_SUR_ERREUR = OUI
NOMBRE_MAX_TENTATIVES_PAR_CAS = 2
DELAI_ENTRE_TENTATIVES = 5.0
SAUVEGARDER_CAPTURES_ERREUR = OUI
DOSSIER_CAPTURES_ERREUR = error_screenshots

# === FENÊTRE CLARIFY ===
ACTIVER_PLEIN_ECRAN = OUI
FORCER_PREMIER_PLAN = OUI
TENTATIVES_MAX_ACTIVATION = 5
PAUSE_MANUELLE_SECONDES = 10

# === CONTRÔLES UTILISATEUR ===
ACTIVER_TOUCHE_ECHAP = OUI
CONFIRMER_AVANT_DEMARRAGE = OUI
AFFICHER_RESUME_FIN = OUI
FERMER_AUTO_FIN = NON

# === MODES SPÉCIAUX ===
MODE_DEBUG = NON
MODE_TEST = NON
TRAITER_SEULEMENT_ECHECS = NON
COMMENCER_A_PARTIR_DU_CAS = 
TAILLE_LOT = 0

# === SUIVI ET LOGS ===
ACTIVER_SUIVI_EXCEL = OUI
ACTIVER_LOGS_CONSOLE = OUI
NIVEAU_LOG = INFO
AFFICHER_BARRE_PROGRESSION = OUI
REPRISE_AUTOMATIQUE = OUI
SAUVEGARDE_FICHIER_SUIVI = OUI

# ========================================
# NOTES D'UTILISATION :
# ========================================
#
# 1. DÉLAIS : Augmentez si votre système est lent
# 2. SEUILS : Diminuez si la détection échoue souvent
# 3. TENTATIVES : Augmentez pour plus de robustesse
# 4. ÉTAPES : Désactivez celles non nécessaires
# 5. FICHIERS : Vérifiez que les chemins sont corrects
#
# Pour appliquer ces changements :
# 1. Modifiez ce fichier
# 2. Exécutez : python convert_config.py
# 3. Le fichier clarify_config.json sera mis à jour
#
# ========================================
