# 🚀 Comment lancer l'interface Clarify

Plusieurs méthodes sont disponibles pour lancer l'interface selon vos préférences.

## 🎯 Méthodes de lancement

### 1. 📁 Double-clic (Recommandé pour Windows)

**Fichier batch (CMD):**
- Double-cliquez sur `Lancer_Clarify_Interface.bat`
- Fonctionne avec l'invite de commandes Windows classique

**Fichier PowerShell:**
- Clic droit sur `Lancer_Clarify_Interface.ps1` → "Exécuter avec PowerShell"
- Ou dans PowerShell : `.\Lancer_Clarify_Interface.ps1`

### 2. ⌨️ Ligne de commande

**Méthode directe:**
```bash
python lancer_interface.py
```

**Méthode alternative:**
```bash
python clarify_gui.py
```

**Avec py.exe (Windows):**
```bash
py lancer_interface.py
```

### 3. 🧪 Test avant lancement

**Vérifier l'installation:**
```bash
python test_interface.py
```

## 🔧 Résolution des problèmes

### Problème : "Python n'est pas reconnu"

**Solutions:**
1. Réinstallez Python en cochant "Add Python to PATH"
2. Utilisez `py` au lieu de `python`
3. Ajoutez Python au PATH manuellement

### Problème : "Fichier non trouvé"

**Solutions:**
1. Vérifiez que vous êtes dans le bon dossier
2. Listez les fichiers : `dir *.py` (Windows) ou `ls *.py` (Linux)
3. Téléchargez à nouveau les fichiers manquants

### Problème : "Module non trouvé"

**Solutions:**
1. Installez les dépendances : `pip install pandas openpyxl pywin32 pyautogui opencv-python pillow`
2. Vérifiez votre environnement Python
3. Utilisez un environnement virtuel si nécessaire

### Problème : Le batch ne fonctionne pas

**Solutions:**
1. Utilisez la version PowerShell : `.\Lancer_Clarify_Interface.ps1`
2. Lancez directement : `python lancer_interface.py`
3. Vérifiez l'encodage du fichier batch

## 📋 Ordre de préférence recommandé

1. **`Lancer_Clarify_Interface.bat`** - Double-clic (Windows CMD)
2. **`Lancer_Clarify_Interface.ps1`** - PowerShell (Windows moderne)
3. **`python lancer_interface.py`** - Ligne de commande directe
4. **`python clarify_gui.py`** - Interface directe (sans vérifications)

## 🎮 Utilisation après lancement

Une fois l'interface ouverte :

1. **📁 Sélectionnez votre fichier Excel** avec le bouton "Parcourir"
2. **🔍 Vérifiez** que Clarify est ouvert
3. **▶️ Cliquez sur START** pour commencer le traitement
4. **📟 Surveillez la console** pour voir le progrès
5. **⏹️ Utilisez STOP** pour arrêter si nécessaire

## 💡 Conseils

- **Testez d'abord** avec quelques IDs pour vérifier le bon fonctionnement
- **Gardez Clarify visible** pendant le traitement
- **Surveillez la console** pour détecter les problèmes
- **Consultez le fichier de suivi** pour les détails des traitements

## 📞 En cas de problème

1. Lancez `python test_interface.py` pour diagnostiquer
2. Vérifiez les messages d'erreur dans la console
3. Consultez les fichiers de documentation (README_Interface.md, INSTALLATION.md)
4. Redémarrez l'interface si nécessaire

---

**Astuce** : Créez un raccourci sur le bureau vers `Lancer_Clarify_Interface.bat` pour un accès rapide !
